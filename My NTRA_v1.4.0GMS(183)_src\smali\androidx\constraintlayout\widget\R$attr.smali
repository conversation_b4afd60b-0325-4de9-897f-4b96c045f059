.class public final Landroidx/constraintlayout/widget/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/widget/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static final SharedValue:I = 0x7f040000

.field public static final SharedValueId:I = 0x7f040001

.field public static final actionBarDivider:I = 0x7f040006

.field public static final actionBarItemBackground:I = 0x7f040007

.field public static final actionBarPopupTheme:I = 0x7f040008

.field public static final actionBarSize:I = 0x7f040009

.field public static final actionBarSplitStyle:I = 0x7f04000a

.field public static final actionBarStyle:I = 0x7f04000b

.field public static final actionBarTabBarStyle:I = 0x7f04000c

.field public static final actionBarTabStyle:I = 0x7f04000d

.field public static final actionBarTabTextStyle:I = 0x7f04000e

.field public static final actionBarTheme:I = 0x7f04000f

.field public static final actionBarWidgetTheme:I = 0x7f040010

.field public static final actionButtonStyle:I = 0x7f040011

.field public static final actionDropDownStyle:I = 0x7f040012

.field public static final actionLayout:I = 0x7f040013

.field public static final actionMenuTextAppearance:I = 0x7f040014

.field public static final actionMenuTextColor:I = 0x7f040015

.field public static final actionModeBackground:I = 0x7f040016

.field public static final actionModeCloseButtonStyle:I = 0x7f040017

.field public static final actionModeCloseDrawable:I = 0x7f040019

.field public static final actionModeCopyDrawable:I = 0x7f04001a

.field public static final actionModeCutDrawable:I = 0x7f04001b

.field public static final actionModeFindDrawable:I = 0x7f04001c

.field public static final actionModePasteDrawable:I = 0x7f04001d

.field public static final actionModePopupWindowStyle:I = 0x7f04001e

.field public static final actionModeSelectAllDrawable:I = 0x7f04001f

.field public static final actionModeShareDrawable:I = 0x7f040020

.field public static final actionModeSplitBackground:I = 0x7f040021

.field public static final actionModeStyle:I = 0x7f040022

.field public static final actionModeWebSearchDrawable:I = 0x7f040024

.field public static final actionOverflowButtonStyle:I = 0x7f040025

.field public static final actionOverflowMenuStyle:I = 0x7f040026

.field public static final actionProviderClass:I = 0x7f040027

.field public static final actionViewClass:I = 0x7f040029

.field public static final activityChooserViewStyle:I = 0x7f04002b

.field public static final alertDialogButtonGroupStyle:I = 0x7f04002e

.field public static final alertDialogCenterButtons:I = 0x7f04002f

.field public static final alertDialogStyle:I = 0x7f040030

.field public static final alertDialogTheme:I = 0x7f040031

.field public static final allowStacking:I = 0x7f040035

.field public static final alpha:I = 0x7f040036

.field public static final alphabeticModifiers:I = 0x7f040037

.field public static final altSrc:I = 0x7f040038

.field public static final animateCircleAngleTo:I = 0x7f04003a

.field public static final animateRelativeTo:I = 0x7f04003b

.field public static final applyMotionScene:I = 0x7f04003e

.field public static final arcMode:I = 0x7f04003f

.field public static final arrowHeadLength:I = 0x7f040041

.field public static final arrowShaftLength:I = 0x7f040042

.field public static final attributeName:I = 0x7f040043

.field public static final autoCompleteMode:I = 0x7f040044

.field public static final autoCompleteTextViewStyle:I = 0x7f040045

.field public static final autoSizeMaxTextSize:I = 0x7f040046

.field public static final autoSizeMinTextSize:I = 0x7f040047

.field public static final autoSizePresetSizes:I = 0x7f040048

.field public static final autoSizeStepGranularity:I = 0x7f040049

.field public static final autoSizeTextType:I = 0x7f04004a

.field public static final autoTransition:I = 0x7f04004b

.field public static final background:I = 0x7f04004c

.field public static final backgroundSplit:I = 0x7f040053

.field public static final backgroundStacked:I = 0x7f040054

.field public static final backgroundTint:I = 0x7f040055

.field public static final backgroundTintMode:I = 0x7f040056

.field public static final barLength:I = 0x7f04005d

.field public static final barrierAllowsGoneWidgets:I = 0x7f04005e

.field public static final barrierDirection:I = 0x7f04005f

.field public static final barrierMargin:I = 0x7f040060

.field public static final blendSrc:I = 0x7f04006c

.field public static final borderRound:I = 0x7f04006d

.field public static final borderRoundPercent:I = 0x7f04006e

.field public static final borderlessButtonStyle:I = 0x7f040070

.field public static final brightness:I = 0x7f040081

.field public static final buttonBarButtonStyle:I = 0x7f040082

.field public static final buttonBarNegativeButtonStyle:I = 0x7f040083

.field public static final buttonBarNeutralButtonStyle:I = 0x7f040084

.field public static final buttonBarPositiveButtonStyle:I = 0x7f040085

.field public static final buttonBarStyle:I = 0x7f040086

.field public static final buttonCompat:I = 0x7f040087

.field public static final buttonGravity:I = 0x7f040088

.field public static final buttonIconDimen:I = 0x7f040089

.field public static final buttonPanelSideLayout:I = 0x7f04008a

.field public static final buttonStyle:I = 0x7f04008c

.field public static final buttonStyleSmall:I = 0x7f04008d

.field public static final buttonTint:I = 0x7f04008e

.field public static final buttonTintMode:I = 0x7f04008f

.field public static final carousel_backwardTransition:I = 0x7f040098

.field public static final carousel_emptyViewsBehavior:I = 0x7f040099

.field public static final carousel_firstView:I = 0x7f04009a

.field public static final carousel_forwardTransition:I = 0x7f04009b

.field public static final carousel_infinite:I = 0x7f04009c

.field public static final carousel_nextState:I = 0x7f04009d

.field public static final carousel_previousState:I = 0x7f04009e

.field public static final carousel_touchUpMode:I = 0x7f04009f

.field public static final carousel_touchUp_dampeningFactor:I = 0x7f0400a0

.field public static final carousel_touchUp_velocityThreshold:I = 0x7f0400a1

.field public static final chainUseRtl:I = 0x7f0400a2

.field public static final checkboxStyle:I = 0x7f0400a7

.field public static final checkedTextViewStyle:I = 0x7f0400b0

.field public static final circleRadius:I = 0x7f0400c6

.field public static final circularflow_angles:I = 0x7f0400c8

.field public static final circularflow_defaultAngle:I = 0x7f0400c9

.field public static final circularflow_defaultRadius:I = 0x7f0400ca

.field public static final circularflow_radiusInDP:I = 0x7f0400cb

.field public static final circularflow_viewCenter:I = 0x7f0400cc

.field public static final clearsTag:I = 0x7f0400ce

.field public static final clickAction:I = 0x7f0400cf

.field public static final closeIcon:I = 0x7f0400d4

.field public static final closeItemLayout:I = 0x7f0400db

.field public static final collapseContentDescription:I = 0x7f0400dc

.field public static final collapseIcon:I = 0x7f0400dd

.field public static final color:I = 0x7f0400e7

.field public static final colorAccent:I = 0x7f0400e8

.field public static final colorBackgroundFloating:I = 0x7f0400e9

.field public static final colorButtonNormal:I = 0x7f0400ea

.field public static final colorControlActivated:I = 0x7f0400ec

.field public static final colorControlHighlight:I = 0x7f0400ed

.field public static final colorControlNormal:I = 0x7f0400ee

.field public static final colorError:I = 0x7f0400ef

.field public static final colorPrimary:I = 0x7f040100

.field public static final colorPrimaryDark:I = 0x7f040102

.field public static final colorSwitchThumbNormal:I = 0x7f04010d

.field public static final commitIcon:I = 0x7f040110

.field public static final constraintRotate:I = 0x7f040111

.field public static final constraintSet:I = 0x7f040112

.field public static final constraintSetEnd:I = 0x7f040113

.field public static final constraintSetStart:I = 0x7f040114

.field public static final constraint_referenced_ids:I = 0x7f040115

.field public static final constraint_referenced_tags:I = 0x7f040116

.field public static final constraints:I = 0x7f040117

.field public static final content:I = 0x7f040118

.field public static final contentDescription:I = 0x7f040119

.field public static final contentInsetEnd:I = 0x7f04011a

.field public static final contentInsetEndWithActions:I = 0x7f04011b

.field public static final contentInsetLeft:I = 0x7f04011c

.field public static final contentInsetRight:I = 0x7f04011d

.field public static final contentInsetStart:I = 0x7f04011e

.field public static final contentInsetStartWithNavigation:I = 0x7f04011f

.field public static final contrast:I = 0x7f040128

.field public static final controlBackground:I = 0x7f040129

.field public static final crossfade:I = 0x7f04013c

.field public static final currentState:I = 0x7f04013d

.field public static final curveFit:I = 0x7f04013e

.field public static final customBoolean:I = 0x7f04013f

.field public static final customColorDrawableValue:I = 0x7f040140

.field public static final customColorValue:I = 0x7f040141

.field public static final customDimension:I = 0x7f040142

.field public static final customFloatValue:I = 0x7f040143

.field public static final customIntegerValue:I = 0x7f040144

.field public static final customNavigationLayout:I = 0x7f040145

.field public static final customPixelDimension:I = 0x7f040146

.field public static final customReference:I = 0x7f040147

.field public static final customStringValue:I = 0x7f040148

.field public static final defaultDuration:I = 0x7f04014f

.field public static final defaultQueryHint:I = 0x7f040151

.field public static final defaultState:I = 0x7f040152

.field public static final deltaPolarAngle:I = 0x7f040154

.field public static final deltaPolarRadius:I = 0x7f040155

.field public static final deriveConstraintsFrom:I = 0x7f040157

.field public static final dialogCornerRadius:I = 0x7f040159

.field public static final dialogPreferredPadding:I = 0x7f04015e

.field public static final dialogTheme:I = 0x7f04015f

.field public static final displayOptions:I = 0x7f040162

.field public static final divider:I = 0x7f040163

.field public static final dividerHorizontal:I = 0x7f040165

.field public static final dividerPadding:I = 0x7f040168

.field public static final dividerVertical:I = 0x7f04016a

.field public static final dragDirection:I = 0x7f04016b

.field public static final dragScale:I = 0x7f04016c

.field public static final dragThreshold:I = 0x7f04016d

.field public static final drawPath:I = 0x7f04016e

.field public static final drawableBottomCompat:I = 0x7f04016f

.field public static final drawableEndCompat:I = 0x7f040170

.field public static final drawableLeftCompat:I = 0x7f040171

.field public static final drawableRightCompat:I = 0x7f040172

.field public static final drawableSize:I = 0x7f040173

.field public static final drawableStartCompat:I = 0x7f040174

.field public static final drawableTint:I = 0x7f040175

.field public static final drawableTintMode:I = 0x7f040176

.field public static final drawableTopCompat:I = 0x7f040177

.field public static final drawerArrowStyle:I = 0x7f040178

.field public static final dropDownListViewStyle:I = 0x7f04017b

.field public static final dropdownListPreferredItemHeight:I = 0x7f04017c

.field public static final duration:I = 0x7f04017e

.field public static final editTextBackground:I = 0x7f040180

.field public static final editTextColor:I = 0x7f040181

.field public static final editTextStyle:I = 0x7f040183

.field public static final elevation:I = 0x7f040184

.field public static final expandActivityOverflowButtonDrawable:I = 0x7f0401a0

.field public static final firstBaselineToTopHeight:I = 0x7f0401c0

.field public static final flow_firstHorizontalBias:I = 0x7f0401cb

.field public static final flow_firstHorizontalStyle:I = 0x7f0401cc

.field public static final flow_firstVerticalBias:I = 0x7f0401cd

.field public static final flow_firstVerticalStyle:I = 0x7f0401ce

.field public static final flow_horizontalAlign:I = 0x7f0401cf

.field public static final flow_horizontalBias:I = 0x7f0401d0

.field public static final flow_horizontalGap:I = 0x7f0401d1

.field public static final flow_horizontalStyle:I = 0x7f0401d2

.field public static final flow_lastHorizontalBias:I = 0x7f0401d3

.field public static final flow_lastHorizontalStyle:I = 0x7f0401d4

.field public static final flow_lastVerticalBias:I = 0x7f0401d5

.field public static final flow_lastVerticalStyle:I = 0x7f0401d6

.field public static final flow_maxElementsWrap:I = 0x7f0401d7

.field public static final flow_padding:I = 0x7f0401d8

.field public static final flow_verticalAlign:I = 0x7f0401d9

.field public static final flow_verticalBias:I = 0x7f0401da

.field public static final flow_verticalGap:I = 0x7f0401db

.field public static final flow_verticalStyle:I = 0x7f0401dc

.field public static final flow_wrapMode:I = 0x7f0401dd

.field public static final font:I = 0x7f0401de

.field public static final fontFamily:I = 0x7f0401df

.field public static final fontProviderAuthority:I = 0x7f0401e0

.field public static final fontProviderCerts:I = 0x7f0401e1

.field public static final fontProviderFetchStrategy:I = 0x7f0401e2

.field public static final fontProviderFetchTimeout:I = 0x7f0401e3

.field public static final fontProviderPackage:I = 0x7f0401e4

.field public static final fontProviderQuery:I = 0x7f0401e5

.field public static final fontStyle:I = 0x7f0401e7

.field public static final fontVariationSettings:I = 0x7f0401e8

.field public static final fontWeight:I = 0x7f0401e9

.field public static final framePosition:I = 0x7f0401ed

.field public static final gapBetweenBars:I = 0x7f0401f0

.field public static final goIcon:I = 0x7f0401f2

.field public static final guidelineUseRtl:I = 0x7f0401f6

.field public static final height:I = 0x7f0401fa

.field public static final hideOnContentScroll:I = 0x7f040201

.field public static final homeAsUpIndicator:I = 0x7f040207

.field public static final homeLayout:I = 0x7f040208

.field public static final icon:I = 0x7f04020c

.field public static final iconTint:I = 0x7f040213

.field public static final iconTintMode:I = 0x7f040214

.field public static final iconifiedByDefault:I = 0x7f040215

.field public static final ifTagNotSet:I = 0x7f040216

.field public static final ifTagSet:I = 0x7f040217

.field public static final imageButtonStyle:I = 0x7f04021a

.field public static final imagePanX:I = 0x7f04021b

.field public static final imagePanY:I = 0x7f04021c

.field public static final imageRotate:I = 0x7f04021d

.field public static final imageZoom:I = 0x7f04021e

.field public static final indeterminateProgressStyle:I = 0x7f040220

.field public static final initialActivityCount:I = 0x7f040226

.field public static final isLightTheme:I = 0x7f040229

.field public static final itemPadding:I = 0x7f040237

.field public static final keyPositionType:I = 0x7f04024b

.field public static final lastBaselineToBottomHeight:I = 0x7f040252

.field public static final layout:I = 0x7f040254

.field public static final layoutDescription:I = 0x7f040255

.field public static final layoutDuringTransition:I = 0x7f040256

.field public static final layout_constrainedHeight:I = 0x7f04025d

.field public static final layout_constrainedWidth:I = 0x7f04025e

.field public static final layout_constraintBaseline_creator:I = 0x7f04025f

.field public static final layout_constraintBaseline_toBaselineOf:I = 0x7f040260

.field public static final layout_constraintBaseline_toBottomOf:I = 0x7f040261

.field public static final layout_constraintBaseline_toTopOf:I = 0x7f040262

.field public static final layout_constraintBottom_creator:I = 0x7f040263

.field public static final layout_constraintBottom_toBottomOf:I = 0x7f040264

.field public static final layout_constraintBottom_toTopOf:I = 0x7f040265

.field public static final layout_constraintCircle:I = 0x7f040266

.field public static final layout_constraintCircleAngle:I = 0x7f040267

.field public static final layout_constraintCircleRadius:I = 0x7f040268

.field public static final layout_constraintDimensionRatio:I = 0x7f040269

.field public static final layout_constraintEnd_toEndOf:I = 0x7f04026a

.field public static final layout_constraintEnd_toStartOf:I = 0x7f04026b

.field public static final layout_constraintGuide_begin:I = 0x7f04026c

.field public static final layout_constraintGuide_end:I = 0x7f04026d

.field public static final layout_constraintGuide_percent:I = 0x7f04026e

.field public static final layout_constraintHeight:I = 0x7f04026f

.field public static final layout_constraintHeight_default:I = 0x7f040270

.field public static final layout_constraintHeight_max:I = 0x7f040271

.field public static final layout_constraintHeight_min:I = 0x7f040272

.field public static final layout_constraintHeight_percent:I = 0x7f040273

.field public static final layout_constraintHorizontal_bias:I = 0x7f040274

.field public static final layout_constraintHorizontal_chainStyle:I = 0x7f040275

.field public static final layout_constraintHorizontal_weight:I = 0x7f040276

.field public static final layout_constraintLeft_creator:I = 0x7f040277

.field public static final layout_constraintLeft_toLeftOf:I = 0x7f040278

.field public static final layout_constraintLeft_toRightOf:I = 0x7f040279

.field public static final layout_constraintRight_creator:I = 0x7f04027a

.field public static final layout_constraintRight_toLeftOf:I = 0x7f04027b

.field public static final layout_constraintRight_toRightOf:I = 0x7f04027c

.field public static final layout_constraintStart_toEndOf:I = 0x7f04027d

.field public static final layout_constraintStart_toStartOf:I = 0x7f04027e

.field public static final layout_constraintTag:I = 0x7f04027f

.field public static final layout_constraintTop_creator:I = 0x7f040280

.field public static final layout_constraintTop_toBottomOf:I = 0x7f040281

.field public static final layout_constraintTop_toTopOf:I = 0x7f040282

.field public static final layout_constraintVertical_bias:I = 0x7f040283

.field public static final layout_constraintVertical_chainStyle:I = 0x7f040284

.field public static final layout_constraintVertical_weight:I = 0x7f040285

.field public static final layout_constraintWidth:I = 0x7f040286

.field public static final layout_constraintWidth_default:I = 0x7f040287

.field public static final layout_constraintWidth_max:I = 0x7f040288

.field public static final layout_constraintWidth_min:I = 0x7f040289

.field public static final layout_constraintWidth_percent:I = 0x7f04028a

.field public static final layout_editor_absoluteX:I = 0x7f04028c

.field public static final layout_editor_absoluteY:I = 0x7f04028d

.field public static final layout_goneMarginBaseline:I = 0x7f04028e

.field public static final layout_goneMarginBottom:I = 0x7f04028f

.field public static final layout_goneMarginEnd:I = 0x7f040290

.field public static final layout_goneMarginLeft:I = 0x7f040291

.field public static final layout_goneMarginRight:I = 0x7f040292

.field public static final layout_goneMarginStart:I = 0x7f040293

.field public static final layout_goneMarginTop:I = 0x7f040294

.field public static final layout_marginBaseline:I = 0x7f040297

.field public static final layout_optimizationLevel:I = 0x7f040298

.field public static final layout_wrapBehaviorInParent:I = 0x7f04029c

.field public static final limitBoundsTo:I = 0x7f04029f

.field public static final lineHeight:I = 0x7f0402a0

.field public static final listChoiceBackgroundIndicator:I = 0x7f0402a3

.field public static final listChoiceIndicatorMultipleAnimated:I = 0x7f0402a4

.field public static final listChoiceIndicatorSingleAnimated:I = 0x7f0402a5

.field public static final listDividerAlertDialog:I = 0x7f0402a6

.field public static final listItemLayout:I = 0x7f0402a7

.field public static final listLayout:I = 0x7f0402a8

.field public static final listMenuViewStyle:I = 0x7f0402a9

.field public static final listPopupWindowStyle:I = 0x7f0402aa

.field public static final listPreferredItemHeight:I = 0x7f0402ab

.field public static final listPreferredItemHeightLarge:I = 0x7f0402ac

.field public static final listPreferredItemHeightSmall:I = 0x7f0402ad

.field public static final listPreferredItemPaddingEnd:I = 0x7f0402ae

.field public static final listPreferredItemPaddingLeft:I = 0x7f0402af

.field public static final listPreferredItemPaddingRight:I = 0x7f0402b0

.field public static final listPreferredItemPaddingStart:I = 0x7f0402b1

.field public static final logo:I = 0x7f0402b2

.field public static final logoDescription:I = 0x7f0402b3

.field public static final maxAcceleration:I = 0x7f0402da

.field public static final maxButtonHeight:I = 0x7f0402dc

.field public static final maxHeight:I = 0x7f0402de

.field public static final maxVelocity:I = 0x7f0402e1

.field public static final maxWidth:I = 0x7f0402e2

.field public static final measureWithLargestChild:I = 0x7f0402e3

.field public static final menu:I = 0x7f0402e4

.field public static final methodName:I = 0x7f0402e6

.field public static final minHeight:I = 0x7f0402e9

.field public static final minWidth:I = 0x7f0402ed

.field public static final mock_diagonalsColor:I = 0x7f0402ee

.field public static final mock_label:I = 0x7f0402ef

.field public static final mock_labelBackgroundColor:I = 0x7f0402f0

.field public static final mock_labelColor:I = 0x7f0402f1

.field public static final mock_showDiagonals:I = 0x7f0402f2

.field public static final mock_showLabel:I = 0x7f0402f3

.field public static final motionDebug:I = 0x7f0402f5

.field public static final motionEffect_alpha:I = 0x7f040301

.field public static final motionEffect_end:I = 0x7f040302

.field public static final motionEffect_move:I = 0x7f040303

.field public static final motionEffect_start:I = 0x7f040304

.field public static final motionEffect_strict:I = 0x7f040305

.field public static final motionEffect_translationX:I = 0x7f040306

.field public static final motionEffect_translationY:I = 0x7f040307

.field public static final motionEffect_viewTransition:I = 0x7f040308

.field public static final motionInterpolator:I = 0x7f040309

.field public static final motionPathRotate:I = 0x7f04030b

.field public static final motionProgress:I = 0x7f04030c

.field public static final motionStagger:I = 0x7f04030d

.field public static final motionTarget:I = 0x7f04030e

.field public static final motion_postLayoutCollision:I = 0x7f04030f

.field public static final motion_triggerOnCollision:I = 0x7f040310

.field public static final moveWhenScrollAtTop:I = 0x7f040311

.field public static final multiChoiceItemLayout:I = 0x7f040312

.field public static final navigationContentDescription:I = 0x7f040314

.field public static final navigationIcon:I = 0x7f040315

.field public static final navigationMode:I = 0x7f040317

.field public static final nestedScrollFlags:I = 0x7f04031b

.field public static final numericModifiers:I = 0x7f040320

.field public static final onCross:I = 0x7f040321

.field public static final onHide:I = 0x7f040322

.field public static final onNegativeCross:I = 0x7f040323

.field public static final onPositiveCross:I = 0x7f040324

.field public static final onShow:I = 0x7f040325

.field public static final onStateTransition:I = 0x7f040326

.field public static final onTouchUp:I = 0x7f040327

.field public static final overlapAnchor:I = 0x7f04032a

.field public static final overlay:I = 0x7f04032b

.field public static final paddingBottomNoButtons:I = 0x7f04032c

.field public static final paddingEnd:I = 0x7f04032e

.field public static final paddingStart:I = 0x7f040331

.field public static final paddingTopNoTitle:I = 0x7f040332

.field public static final panelBackground:I = 0x7f040334

.field public static final panelMenuListTheme:I = 0x7f040335

.field public static final panelMenuListWidth:I = 0x7f040336

.field public static final pathMotionArc:I = 0x7f04033c

.field public static final path_percent:I = 0x7f04033d

.field public static final percentHeight:I = 0x7f04033e

.field public static final percentWidth:I = 0x7f04033f

.field public static final percentX:I = 0x7f040340

.field public static final percentY:I = 0x7f040341

.field public static final perpendicularPath_percent:I = 0x7f040342

.field public static final pivotAnchor:I = 0x7f040344

.field public static final placeholder_emptyVisibility:I = 0x7f040349

.field public static final polarRelativeTo:I = 0x7f04034a

.field public static final popupMenuStyle:I = 0x7f040351

.field public static final popupTheme:I = 0x7f040352

.field public static final popupWindowStyle:I = 0x7f040353

.field public static final preserveIconSpacing:I = 0x7f040361

.field public static final progressBarPadding:I = 0x7f040364

.field public static final progressBarStyle:I = 0x7f040365

.field public static final quantizeMotionInterpolator:I = 0x7f040367

.field public static final quantizeMotionPhase:I = 0x7f040368

.field public static final quantizeMotionSteps:I = 0x7f040369

.field public static final queryBackground:I = 0x7f04036a

.field public static final queryHint:I = 0x7f04036b

.field public static final radioButtonStyle:I = 0x7f04036d

.field public static final ratingBarStyle:I = 0x7f04036f

.field public static final ratingBarStyleIndicator:I = 0x7f040370

.field public static final ratingBarStyleSmall:I = 0x7f040371

.field public static final reactiveGuide_animateChange:I = 0x7f040372

.field public static final reactiveGuide_applyToAllConstraintSets:I = 0x7f040373

.field public static final reactiveGuide_applyToConstraintSet:I = 0x7f040374

.field public static final reactiveGuide_valueId:I = 0x7f040375

.field public static final region_heightLessThan:I = 0x7f040377

.field public static final region_heightMoreThan:I = 0x7f040378

.field public static final region_widthLessThan:I = 0x7f040379

.field public static final region_widthMoreThan:I = 0x7f04037a

.field public static final rotationCenterId:I = 0x7f04037f

.field public static final round:I = 0x7f040380

.field public static final roundPercent:I = 0x7f040381

.field public static final saturation:I = 0x7f040383

.field public static final scaleFromTextSize:I = 0x7f040388

.field public static final searchHintIcon:I = 0x7f04038d

.field public static final searchIcon:I = 0x7f04038e

.field public static final searchViewStyle:I = 0x7f04038f

.field public static final seekBarStyle:I = 0x7f040394

.field public static final selectableItemBackground:I = 0x7f040396

.field public static final selectableItemBackgroundBorderless:I = 0x7f040397

.field public static final setsTag:I = 0x7f04039a

.field public static final showAsAction:I = 0x7f0403a3

.field public static final showDividers:I = 0x7f0403a5

.field public static final showPaths:I = 0x7f0403a7

.field public static final showText:I = 0x7f0403a9

.field public static final showTitle:I = 0x7f0403aa

.field public static final singleChoiceItemLayout:I = 0x7f0403ac

.field public static final sizePercent:I = 0x7f0403b0

.field public static final spinBars:I = 0x7f0403b6

.field public static final spinnerDropDownItemStyle:I = 0x7f0403b7

.field public static final spinnerStyle:I = 0x7f0403b8

.field public static final splitTrack:I = 0x7f0403bd

.field public static final springBoundary:I = 0x7f0403be

.field public static final springDamping:I = 0x7f0403bf

.field public static final springMass:I = 0x7f0403c0

.field public static final springStiffness:I = 0x7f0403c1

.field public static final springStopThreshold:I = 0x7f0403c2

.field public static final srcCompat:I = 0x7f0403c3

.field public static final staggered:I = 0x7f0403c5

.field public static final state_above_anchor:I = 0x7f0403cc

.field public static final subMenuArrow:I = 0x7f0403d7

.field public static final submitBackground:I = 0x7f0403dc

.field public static final subtitle:I = 0x7f0403dd

.field public static final subtitleTextAppearance:I = 0x7f0403df

.field public static final subtitleTextColor:I = 0x7f0403e0

.field public static final subtitleTextStyle:I = 0x7f0403e1

.field public static final suggestionRowLayout:I = 0x7f0403e5

.field public static final switchMinWidth:I = 0x7f040423

.field public static final switchPadding:I = 0x7f040424

.field public static final switchStyle:I = 0x7f040427

.field public static final switchTextAppearance:I = 0x7f040428

.field public static final targetId:I = 0x7f040447

.field public static final telltales_tailColor:I = 0x7f040449

.field public static final telltales_tailScale:I = 0x7f04044a

.field public static final telltales_velocityMode:I = 0x7f04044b

.field public static final textAllCaps:I = 0x7f04044c

.field public static final textAppearanceLargePopupMenu:I = 0x7f040463

.field public static final textAppearanceListItem:I = 0x7f040465

.field public static final textAppearanceListItemSecondary:I = 0x7f040466

.field public static final textAppearanceListItemSmall:I = 0x7f040467

.field public static final textAppearancePopupMenuHeader:I = 0x7f040469

.field public static final textAppearanceSearchResultSubtitle:I = 0x7f04046a

.field public static final textAppearanceSearchResultTitle:I = 0x7f04046b

.field public static final textAppearanceSmallPopupMenu:I = 0x7f04046c

.field public static final textBackground:I = 0x7f040472

.field public static final textBackgroundPanX:I = 0x7f040473

.field public static final textBackgroundPanY:I = 0x7f040474

.field public static final textBackgroundRotate:I = 0x7f040475

.field public static final textBackgroundZoom:I = 0x7f040476

.field public static final textColorAlertDialogListItem:I = 0x7f040477

.field public static final textColorSearchUrl:I = 0x7f040478

.field public static final textFillColor:I = 0x7f04047a

.field public static final textLocale:I = 0x7f040483

.field public static final textOutlineColor:I = 0x7f040484

.field public static final textOutlineThickness:I = 0x7f040485

.field public static final textPanX:I = 0x7f040486

.field public static final textPanY:I = 0x7f040487

.field public static final textureBlurFactor:I = 0x7f040489

.field public static final textureEffect:I = 0x7f04048a

.field public static final textureHeight:I = 0x7f04048b

.field public static final textureWidth:I = 0x7f04048c

.field public static final theme:I = 0x7f04048d

.field public static final thickness:I = 0x7f04048f

.field public static final thumbTextPadding:I = 0x7f040495

.field public static final thumbTint:I = 0x7f040496

.field public static final thumbTintMode:I = 0x7f040497

.field public static final tickMark:I = 0x7f04049b

.field public static final tickMarkTint:I = 0x7f04049c

.field public static final tickMarkTintMode:I = 0x7f04049d

.field public static final tint:I = 0x7f04049f

.field public static final tintMode:I = 0x7f0404a0

.field public static final title:I = 0x7f0404a1

.field public static final titleMargin:I = 0x7f0404a5

.field public static final titleMarginBottom:I = 0x7f0404a6

.field public static final titleMarginEnd:I = 0x7f0404a7

.field public static final titleMarginStart:I = 0x7f0404a8

.field public static final titleMarginTop:I = 0x7f0404a9

.field public static final titleMargins:I = 0x7f0404aa

.field public static final titleTextAppearance:I = 0x7f0404ac

.field public static final titleTextColor:I = 0x7f0404ad

.field public static final titleTextStyle:I = 0x7f0404ae

.field public static final toolbarNavigationButtonStyle:I = 0x7f0404b7

.field public static final toolbarStyle:I = 0x7f0404b8

.field public static final tooltipForegroundColor:I = 0x7f0404ba

.field public static final tooltipFrameBackground:I = 0x7f0404bb

.field public static final tooltipText:I = 0x7f0404bd

.field public static final touchAnchorId:I = 0x7f0404bf

.field public static final touchAnchorSide:I = 0x7f0404c0

.field public static final touchRegionId:I = 0x7f0404c1

.field public static final track:I = 0x7f0404c2

.field public static final trackTint:I = 0x7f0404c9

.field public static final trackTintMode:I = 0x7f0404ca

.field public static final transformPivotTarget:I = 0x7f0404cb

.field public static final transitionDisable:I = 0x7f0404cc

.field public static final transitionEasing:I = 0x7f0404cd

.field public static final transitionFlags:I = 0x7f0404ce

.field public static final transitionPathRotate:I = 0x7f0404cf

.field public static final triggerId:I = 0x7f0404d1

.field public static final triggerReceiver:I = 0x7f0404d2

.field public static final triggerSlack:I = 0x7f0404d3

.field public static final ttcIndex:I = 0x7f0404d4

.field public static final upDuration:I = 0x7f0404d5

.field public static final viewInflaterClass:I = 0x7f0404e4

.field public static final viewTransitionMode:I = 0x7f0404e5

.field public static final viewTransitionOnCross:I = 0x7f0404e6

.field public static final viewTransitionOnNegativeCross:I = 0x7f0404e7

.field public static final viewTransitionOnPositiveCross:I = 0x7f0404e8

.field public static final visibilityMode:I = 0x7f0404e9

.field public static final voiceIcon:I = 0x7f0404ea

.field public static final warmth:I = 0x7f0404eb

.field public static final waveDecay:I = 0x7f0404ec

.field public static final waveOffset:I = 0x7f0404ed

.field public static final wavePeriod:I = 0x7f0404ee

.field public static final wavePhase:I = 0x7f0404ef

.field public static final waveShape:I = 0x7f0404f0

.field public static final waveVariesBy:I = 0x7f0404f1

.field public static final windowActionBar:I = 0x7f0404f3

.field public static final windowActionBarOverlay:I = 0x7f0404f4

.field public static final windowActionModeOverlay:I = 0x7f0404f5

.field public static final windowFixedHeightMajor:I = 0x7f0404f6

.field public static final windowFixedHeightMinor:I = 0x7f0404f7

.field public static final windowFixedWidthMajor:I = 0x7f0404f8

.field public static final windowFixedWidthMinor:I = 0x7f0404f9

.field public static final windowMinWidthMajor:I = 0x7f0404fa

.field public static final windowMinWidthMinor:I = 0x7f0404fb

.field public static final windowNoTitle:I = 0x7f0404fc


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
