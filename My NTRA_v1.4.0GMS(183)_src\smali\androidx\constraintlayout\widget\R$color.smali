.class public final Landroidx/constraintlayout/widget/R$color;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/widget/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "color"
.end annotation


# static fields
.field public static final abc_background_cache_hint_selector_material_dark:I = 0x7f060000

.field public static final abc_background_cache_hint_selector_material_light:I = 0x7f060001

.field public static final abc_btn_colored_borderless_text_material:I = 0x7f060002

.field public static final abc_btn_colored_text_material:I = 0x7f060003

.field public static final abc_color_highlight_material:I = 0x7f060004

.field public static final abc_decor_view_status_guard:I = 0x7f060005

.field public static final abc_decor_view_status_guard_light:I = 0x7f060006

.field public static final abc_hint_foreground_material_dark:I = 0x7f060007

.field public static final abc_hint_foreground_material_light:I = 0x7f060008

.field public static final abc_primary_text_disable_only_material_dark:I = 0x7f060009

.field public static final abc_primary_text_disable_only_material_light:I = 0x7f06000a

.field public static final abc_primary_text_material_dark:I = 0x7f06000b

.field public static final abc_primary_text_material_light:I = 0x7f06000c

.field public static final abc_search_url_text:I = 0x7f06000d

.field public static final abc_search_url_text_normal:I = 0x7f06000e

.field public static final abc_search_url_text_pressed:I = 0x7f06000f

.field public static final abc_search_url_text_selected:I = 0x7f060010

.field public static final abc_secondary_text_material_dark:I = 0x7f060011

.field public static final abc_secondary_text_material_light:I = 0x7f060012

.field public static final abc_tint_btn_checkable:I = 0x7f060013

.field public static final abc_tint_default:I = 0x7f060014

.field public static final abc_tint_edittext:I = 0x7f060015

.field public static final abc_tint_seek_thumb:I = 0x7f060016

.field public static final abc_tint_spinner:I = 0x7f060017

.field public static final abc_tint_switch_track:I = 0x7f060018

.field public static final accent_material_dark:I = 0x7f060019

.field public static final accent_material_light:I = 0x7f06001a

.field public static final androidx_core_ripple_material_light:I = 0x7f06001b

.field public static final androidx_core_secondary_text_default_material_light:I = 0x7f06001c

.field public static final background_floating_material_dark:I = 0x7f06001d

.field public static final background_floating_material_light:I = 0x7f06001e

.field public static final background_material_dark:I = 0x7f06001f

.field public static final background_material_light:I = 0x7f060020

.field public static final bright_foreground_disabled_material_dark:I = 0x7f060026

.field public static final bright_foreground_disabled_material_light:I = 0x7f060027

.field public static final bright_foreground_inverse_material_dark:I = 0x7f060028

.field public static final bright_foreground_inverse_material_light:I = 0x7f060029

.field public static final bright_foreground_material_dark:I = 0x7f06002a

.field public static final bright_foreground_material_light:I = 0x7f06002b

.field public static final button_material_dark:I = 0x7f060030

.field public static final button_material_light:I = 0x7f060031

.field public static final dim_foreground_disabled_material_dark:I = 0x7f06006f

.field public static final dim_foreground_disabled_material_light:I = 0x7f060070

.field public static final dim_foreground_material_dark:I = 0x7f060071

.field public static final dim_foreground_material_light:I = 0x7f060072

.field public static final error_color_material_dark:I = 0x7f060073

.field public static final error_color_material_light:I = 0x7f060074

.field public static final foreground_material_dark:I = 0x7f060075

.field public static final foreground_material_light:I = 0x7f060076

.field public static final highlighted_text_material_dark:I = 0x7f060083

.field public static final highlighted_text_material_light:I = 0x7f060084

.field public static final material_blue_grey_800:I = 0x7f0601c6

.field public static final material_blue_grey_900:I = 0x7f0601c7

.field public static final material_blue_grey_950:I = 0x7f0601c8

.field public static final material_deep_teal_200:I = 0x7f0601ca

.field public static final material_deep_teal_500:I = 0x7f0601cb

.field public static final material_grey_100:I = 0x7f06020e

.field public static final material_grey_300:I = 0x7f06020f

.field public static final material_grey_50:I = 0x7f060210

.field public static final material_grey_600:I = 0x7f060211

.field public static final material_grey_800:I = 0x7f060212

.field public static final material_grey_850:I = 0x7f060213

.field public static final material_grey_900:I = 0x7f060214

.field public static final notification_action_color_filter:I = 0x7f06025d

.field public static final notification_icon_bg_color:I = 0x7f06025e

.field public static final primary_dark_material_dark:I = 0x7f060269

.field public static final primary_dark_material_light:I = 0x7f06026a

.field public static final primary_material_dark:I = 0x7f06026b

.field public static final primary_material_light:I = 0x7f06026c

.field public static final primary_text_default_material_dark:I = 0x7f06026d

.field public static final primary_text_default_material_light:I = 0x7f06026e

.field public static final primary_text_disabled_material_dark:I = 0x7f06026f

.field public static final primary_text_disabled_material_light:I = 0x7f060270

.field public static final ripple_material_dark:I = 0x7f060274

.field public static final ripple_material_light:I = 0x7f060275

.field public static final secondary_text_default_material_dark:I = 0x7f060276

.field public static final secondary_text_default_material_light:I = 0x7f060277

.field public static final secondary_text_disabled_material_dark:I = 0x7f060278

.field public static final secondary_text_disabled_material_light:I = 0x7f060279

.field public static final switch_thumb_disabled_material_dark:I = 0x7f06027a

.field public static final switch_thumb_disabled_material_light:I = 0x7f06027b

.field public static final switch_thumb_material_dark:I = 0x7f06027c

.field public static final switch_thumb_material_light:I = 0x7f06027d

.field public static final switch_thumb_normal_material_dark:I = 0x7f06027e

.field public static final switch_thumb_normal_material_light:I = 0x7f06027f

.field public static final tooltip_background_dark:I = 0x7f060286

.field public static final tooltip_background_light:I = 0x7f060287


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
