.class public final Landroidx/constraintlayout/widget/R$dimen;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/widget/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "dimen"
.end annotation


# static fields
.field public static final abc_action_bar_content_inset_material:I = 0x7f0702f8

.field public static final abc_action_bar_content_inset_with_nav:I = 0x7f0702f9

.field public static final abc_action_bar_default_height_material:I = 0x7f0702fa

.field public static final abc_action_bar_default_padding_end_material:I = 0x7f0702fb

.field public static final abc_action_bar_default_padding_start_material:I = 0x7f0702fc

.field public static final abc_action_bar_elevation_material:I = 0x7f0702fd

.field public static final abc_action_bar_icon_vertical_padding_material:I = 0x7f0702fe

.field public static final abc_action_bar_overflow_padding_end_material:I = 0x7f0702ff

.field public static final abc_action_bar_overflow_padding_start_material:I = 0x7f070300

.field public static final abc_action_bar_stacked_max_height:I = 0x7f070301

.field public static final abc_action_bar_stacked_tab_max_width:I = 0x7f070302

.field public static final abc_action_bar_subtitle_bottom_margin_material:I = 0x7f070303

.field public static final abc_action_bar_subtitle_top_margin_material:I = 0x7f070304

.field public static final abc_action_button_min_height_material:I = 0x7f070305

.field public static final abc_action_button_min_width_material:I = 0x7f070306

.field public static final abc_action_button_min_width_overflow_material:I = 0x7f070307

.field public static final abc_alert_dialog_button_bar_height:I = 0x7f070308

.field public static final abc_alert_dialog_button_dimen:I = 0x7f070309

.field public static final abc_button_inset_horizontal_material:I = 0x7f07030a

.field public static final abc_button_inset_vertical_material:I = 0x7f07030b

.field public static final abc_button_padding_horizontal_material:I = 0x7f07030c

.field public static final abc_button_padding_vertical_material:I = 0x7f07030d

.field public static final abc_cascading_menus_min_smallest_width:I = 0x7f07030e

.field public static final abc_config_prefDialogWidth:I = 0x7f07030f

.field public static final abc_control_corner_material:I = 0x7f070310

.field public static final abc_control_inset_material:I = 0x7f070311

.field public static final abc_control_padding_material:I = 0x7f070312

.field public static final abc_dialog_corner_radius_material:I = 0x7f070313

.field public static final abc_dialog_fixed_height_major:I = 0x7f070314

.field public static final abc_dialog_fixed_height_minor:I = 0x7f070315

.field public static final abc_dialog_fixed_width_major:I = 0x7f070316

.field public static final abc_dialog_fixed_width_minor:I = 0x7f070317

.field public static final abc_dialog_list_padding_bottom_no_buttons:I = 0x7f070318

.field public static final abc_dialog_list_padding_top_no_title:I = 0x7f070319

.field public static final abc_dialog_min_width_major:I = 0x7f07031a

.field public static final abc_dialog_min_width_minor:I = 0x7f07031b

.field public static final abc_dialog_padding_material:I = 0x7f07031c

.field public static final abc_dialog_padding_top_material:I = 0x7f07031d

.field public static final abc_dialog_title_divider_material:I = 0x7f07031e

.field public static final abc_disabled_alpha_material_dark:I = 0x7f07031f

.field public static final abc_disabled_alpha_material_light:I = 0x7f070320

.field public static final abc_dropdownitem_icon_width:I = 0x7f070321

.field public static final abc_dropdownitem_text_padding_left:I = 0x7f070322

.field public static final abc_dropdownitem_text_padding_right:I = 0x7f070323

.field public static final abc_edit_text_inset_bottom_material:I = 0x7f070324

.field public static final abc_edit_text_inset_horizontal_material:I = 0x7f070325

.field public static final abc_edit_text_inset_top_material:I = 0x7f070326

.field public static final abc_floating_window_z:I = 0x7f070327

.field public static final abc_list_item_height_large_material:I = 0x7f070328

.field public static final abc_list_item_height_material:I = 0x7f070329

.field public static final abc_list_item_height_small_material:I = 0x7f07032a

.field public static final abc_list_item_padding_horizontal_material:I = 0x7f07032b

.field public static final abc_panel_menu_list_width:I = 0x7f07032c

.field public static final abc_progress_bar_height_material:I = 0x7f07032d

.field public static final abc_search_view_preferred_height:I = 0x7f07032e

.field public static final abc_search_view_preferred_width:I = 0x7f07032f

.field public static final abc_seekbar_track_background_height_material:I = 0x7f070330

.field public static final abc_seekbar_track_progress_height_material:I = 0x7f070331

.field public static final abc_select_dialog_padding_start_material:I = 0x7f070332

.field public static final abc_switch_padding:I = 0x7f070336

.field public static final abc_text_size_body_1_material:I = 0x7f070337

.field public static final abc_text_size_body_2_material:I = 0x7f070338

.field public static final abc_text_size_button_material:I = 0x7f070339

.field public static final abc_text_size_caption_material:I = 0x7f07033a

.field public static final abc_text_size_display_1_material:I = 0x7f07033b

.field public static final abc_text_size_display_2_material:I = 0x7f07033c

.field public static final abc_text_size_display_3_material:I = 0x7f07033d

.field public static final abc_text_size_display_4_material:I = 0x7f07033e

.field public static final abc_text_size_headline_material:I = 0x7f07033f

.field public static final abc_text_size_large_material:I = 0x7f070340

.field public static final abc_text_size_medium_material:I = 0x7f070341

.field public static final abc_text_size_menu_header_material:I = 0x7f070342

.field public static final abc_text_size_menu_material:I = 0x7f070343

.field public static final abc_text_size_small_material:I = 0x7f070344

.field public static final abc_text_size_subhead_material:I = 0x7f070345

.field public static final abc_text_size_subtitle_material_toolbar:I = 0x7f070346

.field public static final abc_text_size_title_material:I = 0x7f070347

.field public static final abc_text_size_title_material_toolbar:I = 0x7f070348

.field public static final compat_button_inset_horizontal_material:I = 0x7f070352

.field public static final compat_button_inset_vertical_material:I = 0x7f070353

.field public static final compat_button_padding_horizontal_material:I = 0x7f070354

.field public static final compat_button_padding_vertical_material:I = 0x7f070355

.field public static final compat_control_corner_material:I = 0x7f070356

.field public static final compat_notification_large_icon_max_height:I = 0x7f070357

.field public static final compat_notification_large_icon_max_width:I = 0x7f070358

.field public static final disabled_alpha_material_dark:I = 0x7f07038d

.field public static final disabled_alpha_material_light:I = 0x7f07038e

.field public static final highlight_alpha_material_colored:I = 0x7f070395

.field public static final highlight_alpha_material_dark:I = 0x7f070396

.field public static final highlight_alpha_material_light:I = 0x7f070397

.field public static final hint_alpha_material_dark:I = 0x7f070398

.field public static final hint_alpha_material_light:I = 0x7f070399

.field public static final hint_pressed_alpha_material_dark:I = 0x7f07039a

.field public static final hint_pressed_alpha_material_light:I = 0x7f07039b

.field public static final notification_action_icon_size:I = 0x7f070528

.field public static final notification_action_text_size:I = 0x7f070529

.field public static final notification_big_circle_margin:I = 0x7f07052a

.field public static final notification_content_margin_start:I = 0x7f07052b

.field public static final notification_large_icon_height:I = 0x7f07052c

.field public static final notification_large_icon_width:I = 0x7f07052d

.field public static final notification_main_column_padding_top:I = 0x7f07052e

.field public static final notification_media_narrow_margin:I = 0x7f07052f

.field public static final notification_right_icon_size:I = 0x7f070530

.field public static final notification_right_side_padding_top:I = 0x7f070531

.field public static final notification_small_icon_background_padding:I = 0x7f070532

.field public static final notification_small_icon_size_as_large:I = 0x7f070533

.field public static final notification_subtext_size:I = 0x7f070534

.field public static final notification_top_pad:I = 0x7f070535

.field public static final notification_top_pad_large_text:I = 0x7f070536

.field public static final tooltip_corner_radius:I = 0x7f070551

.field public static final tooltip_horizontal_padding:I = 0x7f070552

.field public static final tooltip_margin:I = 0x7f070553

.field public static final tooltip_precise_anchor_extra_offset:I = 0x7f070554

.field public static final tooltip_precise_anchor_threshold:I = 0x7f070555

.field public static final tooltip_vertical_padding:I = 0x7f070556

.field public static final tooltip_y_offset_non_touch:I = 0x7f070557

.field public static final tooltip_y_offset_touch:I = 0x7f070558


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
