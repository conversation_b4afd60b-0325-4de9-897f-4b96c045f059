.class public final Landroidx/constraintlayout/widget/R$interpolator;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/widget/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "interpolator"
.end annotation


# static fields
.field public static final btn_checkbox_checked_mtrl_animation_interpolator_0:I = 0x7f0c0000

.field public static final btn_checkbox_checked_mtrl_animation_interpolator_1:I = 0x7f0c0001

.field public static final btn_checkbox_unchecked_mtrl_animation_interpolator_0:I = 0x7f0c0002

.field public static final btn_checkbox_unchecked_mtrl_animation_interpolator_1:I = 0x7f0c0003

.field public static final btn_radio_to_off_mtrl_animation_interpolator_0:I = 0x7f0c0004

.field public static final btn_radio_to_on_mtrl_animation_interpolator_0:I = 0x7f0c0005

.field public static final fast_out_slow_in:I = 0x7f0c0006


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
