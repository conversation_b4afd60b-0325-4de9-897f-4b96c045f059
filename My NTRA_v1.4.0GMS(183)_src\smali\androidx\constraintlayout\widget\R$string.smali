.class public final Landroidx/constraintlayout/widget/R$string;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/constraintlayout/widget/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "string"
.end annotation


# static fields
.field public static final abc_action_bar_home_description:I = 0x7f130000

.field public static final abc_action_bar_up_description:I = 0x7f130001

.field public static final abc_action_menu_overflow_description:I = 0x7f130002

.field public static final abc_action_mode_done:I = 0x7f130003

.field public static final abc_activity_chooser_view_see_all:I = 0x7f130004

.field public static final abc_activitychooserview_choose_application:I = 0x7f130005

.field public static final abc_capital_off:I = 0x7f130006

.field public static final abc_capital_on:I = 0x7f130007

.field public static final abc_menu_alt_shortcut_label:I = 0x7f130008

.field public static final abc_menu_ctrl_shortcut_label:I = 0x7f130009

.field public static final abc_menu_delete_shortcut_label:I = 0x7f13000a

.field public static final abc_menu_enter_shortcut_label:I = 0x7f13000b

.field public static final abc_menu_function_shortcut_label:I = 0x7f13000c

.field public static final abc_menu_meta_shortcut_label:I = 0x7f13000d

.field public static final abc_menu_shift_shortcut_label:I = 0x7f13000e

.field public static final abc_menu_space_shortcut_label:I = 0x7f13000f

.field public static final abc_menu_sym_shortcut_label:I = 0x7f130010

.field public static final abc_prepend_shortcut_label:I = 0x7f130011

.field public static final abc_search_hint:I = 0x7f130012

.field public static final abc_searchview_description_clear:I = 0x7f130013

.field public static final abc_searchview_description_query:I = 0x7f130014

.field public static final abc_searchview_description_search:I = 0x7f130015

.field public static final abc_searchview_description_submit:I = 0x7f130016

.field public static final abc_searchview_description_voice:I = 0x7f130017

.field public static final abc_shareactionprovider_share_with:I = 0x7f130018

.field public static final abc_shareactionprovider_share_with_application:I = 0x7f130019

.field public static final abc_toolbar_collapse_description:I = 0x7f13001a

.field public static final search_menu_title:I = 0x7f13020b

.field public static final status_bar_notification_info_overflow:I = 0x7f130234


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
