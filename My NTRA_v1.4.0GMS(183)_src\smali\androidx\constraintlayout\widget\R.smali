.class public final Landroidx/constraintlayout/widget/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/constraintlayout/widget/R$anim;,
        Landroidx/constraintlayout/widget/R$attr;,
        Landroidx/constraintlayout/widget/R$bool;,
        Landroidx/constraintlayout/widget/R$color;,
        Landroidx/constraintlayout/widget/R$dimen;,
        Landroidx/constraintlayout/widget/R$drawable;,
        Landroidx/constraintlayout/widget/R$id;,
        Landroidx/constraintlayout/widget/R$integer;,
        Landroidx/constraintlayout/widget/R$interpolator;,
        Landroidx/constraintlayout/widget/R$layout;,
        Landroidx/constraintlayout/widget/R$string;,
        Landroidx/constraintlayout/widget/R$style;,
        Landroidx/constraintlayout/widget/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
