.class public final Landroidx/coordinatorlayout/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/coordinatorlayout/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static final alpha:I = 0x7f040036

.field public static final coordinatorLayoutStyle:I = 0x7f04012a

.field public static final font:I = 0x7f0401de

.field public static final fontProviderAuthority:I = 0x7f0401e0

.field public static final fontProviderCerts:I = 0x7f0401e1

.field public static final fontProviderFetchStrategy:I = 0x7f0401e2

.field public static final fontProviderFetchTimeout:I = 0x7f0401e3

.field public static final fontProviderPackage:I = 0x7f0401e4

.field public static final fontProviderQuery:I = 0x7f0401e5

.field public static final fontStyle:I = 0x7f0401e7

.field public static final fontVariationSettings:I = 0x7f0401e8

.field public static final fontWeight:I = 0x7f0401e9

.field public static final keylines:I = 0x7f04024d

.field public static final layout_anchor:I = 0x7f040258

.field public static final layout_anchorGravity:I = 0x7f040259

.field public static final layout_behavior:I = 0x7f04025a

.field public static final layout_dodgeInsetEdges:I = 0x7f04028b

.field public static final layout_insetEdge:I = 0x7f040295

.field public static final layout_keyline:I = 0x7f040296

.field public static final statusBarBackground:I = 0x7f0403d2

.field public static final ttcIndex:I = 0x7f0404d4


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
