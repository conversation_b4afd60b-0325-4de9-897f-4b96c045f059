.class public final Landroidx/coordinatorlayout/R$color;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/coordinatorlayout/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "color"
.end annotation


# static fields
.field public static final notification_action_color_filter:I = 0x7f06025d

.field public static final notification_icon_bg_color:I = 0x7f06025e

.field public static final ripple_material_light:I = 0x7f060275

.field public static final secondary_text_default_material_light:I = 0x7f060277


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
