.class public final Landroidx/coordinatorlayout/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/coordinatorlayout/R$attr;,
        Landroidx/coordinatorlayout/R$color;,
        Landroidx/coordinatorlayout/R$dimen;,
        Landroidx/coordinatorlayout/R$drawable;,
        Landroidx/coordinatorlayout/R$id;,
        Landroidx/coordinatorlayout/R$integer;,
        Landroidx/coordinatorlayout/R$layout;,
        Landroidx/coordinatorlayout/R$string;,
        Landroidx/coordinatorlayout/R$style;,
        Landroidx/coordinatorlayout/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
