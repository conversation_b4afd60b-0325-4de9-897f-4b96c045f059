.class public interface abstract Landroidx/coordinatorlayout/widget/CoordinatorLayout$AttachedBehavior;
.super Ljava/lang/Object;
.source "CoordinatorLayout.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/coordinatorlayout/widget/CoordinatorLayout;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "AttachedBehavior"
.end annotation


# virtual methods
.method public abstract getBehavior()Landroidx/coordinatorlayout/widget/CoordinatorLayout$Behavior;
.end method
