.class public interface abstract Landroidx/core/app/CoreComponentFactory$CompatWrapped;
.super Ljava/lang/Object;
.source "CoreComponentFactory.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/app/CoreComponentFactory;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "CompatWrapped"
.end annotation


# virtual methods
.method public abstract getWrapper()Ljava/lang/Object;
.end method
