.class interface abstract Landroidx/core/app/JobIntentService$GenericWorkItem;
.super Ljava/lang/Object;
.source "JobIntentService.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/app/JobIntentService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "GenericWorkItem"
.end annotation


# virtual methods
.method public abstract complete()V
.end method

.method public abstract getIntent()Landroid/content/Intent;
.end method
