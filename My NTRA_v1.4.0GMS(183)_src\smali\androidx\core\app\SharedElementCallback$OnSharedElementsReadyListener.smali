.class public interface abstract Landroidx/core/app/SharedElementCallback$OnSharedElementsReadyListener;
.super Ljava/lang/Object;
.source "SharedElementCallback.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/app/SharedElementCallback;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnSharedElementsReadyListener"
.end annotation


# virtual methods
.method public abstract onSharedElementsReady()V
.end method
