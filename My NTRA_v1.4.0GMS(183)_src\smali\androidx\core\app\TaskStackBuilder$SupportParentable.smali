.class public interface abstract Landroidx/core/app/TaskStackBuilder$SupportParentable;
.super Ljava/lang/Object;
.source "TaskStackBuilder.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/app/TaskStackBuilder;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "SupportParentable"
.end annotation


# virtual methods
.method public abstract getSupportParentActivityIntent()Landroid/content/Intent;
.end method
