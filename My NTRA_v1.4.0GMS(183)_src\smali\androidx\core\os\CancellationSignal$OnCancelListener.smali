.class public interface abstract Landroidx/core/os/CancellationSignal$OnCancelListener;
.super Ljava/lang/Object;
.source "CancellationSignal.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/os/CancellationSignal;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnCancelListener"
.end annotation


# virtual methods
.method public abstract onCancel()V
.end method
