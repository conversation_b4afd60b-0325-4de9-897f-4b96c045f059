.class interface abstract Landroidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper;
.super Ljava/lang/Object;
.source "TileServiceCompat.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/service/quicksettings/TileServiceCompat;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "TileServiceWrapper"
.end annotation


# virtual methods
.method public abstract startActivityAndCollapse(Landroid/app/PendingIntent;)V
.end method

.method public abstract startActivityAndCollapse(Landroid/content/Intent;)V
.end method
