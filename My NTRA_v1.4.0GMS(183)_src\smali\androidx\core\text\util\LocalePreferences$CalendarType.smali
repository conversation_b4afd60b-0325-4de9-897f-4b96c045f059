.class public Landroidx/core/text/util/LocalePreferences$CalendarType;
.super Ljava/lang/Object;
.source "LocalePreferences.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/text/util/LocalePreferences;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "CalendarType"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes;
    }
.end annotation


# static fields
.field public static final CHINESE:Ljava/lang/String; = "chinese"

.field public static final DANGI:Ljava/lang/String; = "dangi"

.field public static final DEFAULT:Ljava/lang/String; = ""

.field public static final GREGORIAN:Ljava/lang/String; = "gregorian"

.field public static final HEBREW:Ljava/lang/String; = "hebrew"

.field public static final INDIAN:Ljava/lang/String; = "indian"

.field public static final ISLAMIC:Ljava/lang/String; = "islamic"

.field public static final ISLAMIC_CIVIL:Ljava/lang/String; = "islamic-civil"

.field public static final ISLAMIC_RGSA:Ljava/lang/String; = "islamic-rgsa"

.field public static final ISLAMIC_TBLA:Ljava/lang/String; = "islamic-tbla"

.field public static final ISLAMIC_UMALQURA:Ljava/lang/String; = "islamic-umalqura"

.field public static final PERSIAN:Ljava/lang/String; = "persian"

.field private static final U_EXTENSION_TAG:Ljava/lang/String; = "ca"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 203
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
