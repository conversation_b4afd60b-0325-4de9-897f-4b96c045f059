.class public Landroidx/core/text/util/LocalePreferences$FirstDayOfWeek;
.super Ljava/lang/Object;
.source "LocalePreferences.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/text/util/LocalePreferences;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "FirstDayOfWeek"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days;
    }
.end annotation


# static fields
.field public static final DEFAULT:Ljava/lang/String; = ""

.field public static final FRIDAY:Ljava/lang/String; = "fri"

.field public static final MONDAY:Ljava/lang/String; = "mon"

.field public static final SATURDAY:Ljava/lang/String; = "sat"

.field public static final SUNDAY:Ljava/lang/String; = "sun"

.field public static final THURSDAY:Ljava/lang/String; = "thu"

.field public static final TUESDAY:Ljava/lang/String; = "tue"

.field private static final U_EXTENSION_TAG:Ljava/lang/String; = "fw"

.field public static final WEDNESDAY:Ljava/lang/String; = "wed"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 426
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
