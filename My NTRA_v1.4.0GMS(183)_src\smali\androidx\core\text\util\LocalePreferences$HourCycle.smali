.class public Landroidx/core/text/util/LocalePreferences$HourCycle;
.super Ljava/lang/Object;
.source "LocalePreferences.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/text/util/LocalePreferences;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "HourCycle"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes;
    }
.end annotation


# static fields
.field public static final DEFAULT:Ljava/lang/String; = ""

.field public static final H11:Ljava/lang/String; = "h11"

.field public static final H12:Ljava/lang/String; = "h12"

.field public static final H23:Ljava/lang/String; = "h23"

.field public static final H24:Ljava/lang/String; = "h24"

.field private static final U_EXTENSION_TAG:Ljava/lang/String; = "hc"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 74
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
