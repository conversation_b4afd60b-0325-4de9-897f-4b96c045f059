.class public Landroidx/core/text/util/LocalePreferences$TemperatureUnit;
.super Ljava/lang/Object;
.source "LocalePreferences.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/core/text/util/LocalePreferences;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "TemperatureUnit"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits;
    }
.end annotation


# static fields
.field public static final CELSIUS:Ljava/lang/String; = "celsius"

.field public static final DEFAULT:Ljava/lang/String; = ""

.field public static final FAHRENHEIT:Ljava/lang/String; = "fahrenhe"

.field public static final KELVIN:Ljava/lang/String; = "kelvin"

.field private static final U_EXTENSION_TAG:Ljava/lang/String; = "mu"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 308
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
