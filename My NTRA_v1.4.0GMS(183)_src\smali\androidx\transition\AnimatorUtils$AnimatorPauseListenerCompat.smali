.class interface abstract Landroidx/transition/AnimatorUtils$AnimatorPauseListenerCompat;
.super Ljava/lang/Object;
.source "AnimatorUtils.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/AnimatorUtils;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "AnimatorPauseListenerCompat"
.end annotation


# virtual methods
.method public abstract onAnimationPause(Landroid/animation/Animator;)V
.end method

.method public abstract onAnimationResume(Landroid/animation/Animator;)V
.end method
