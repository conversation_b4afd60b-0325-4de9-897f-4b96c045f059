.class public final Landroidx/transition/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static final actionBarDivider:I = 0x7f040006

.field public static final actionBarItemBackground:I = 0x7f040007

.field public static final actionBarPopupTheme:I = 0x7f040008

.field public static final actionBarSize:I = 0x7f040009

.field public static final actionBarSplitStyle:I = 0x7f04000a

.field public static final actionBarStyle:I = 0x7f04000b

.field public static final actionBarTabBarStyle:I = 0x7f04000c

.field public static final actionBarTabStyle:I = 0x7f04000d

.field public static final actionBarTabTextStyle:I = 0x7f04000e

.field public static final actionBarTheme:I = 0x7f04000f

.field public static final actionBarWidgetTheme:I = 0x7f040010

.field public static final actionButtonStyle:I = 0x7f040011

.field public static final actionDropDownStyle:I = 0x7f040012

.field public static final actionLayout:I = 0x7f040013

.field public static final actionMenuTextAppearance:I = 0x7f040014

.field public static final actionMenuTextColor:I = 0x7f040015

.field public static final actionModeBackground:I = 0x7f040016

.field public static final actionModeCloseButtonStyle:I = 0x7f040017

.field public static final actionModeCloseDrawable:I = 0x7f040019

.field public static final actionModeCopyDrawable:I = 0x7f04001a

.field public static final actionModeCutDrawable:I = 0x7f04001b

.field public static final actionModeFindDrawable:I = 0x7f04001c

.field public static final actionModePasteDrawable:I = 0x7f04001d

.field public static final actionModePopupWindowStyle:I = 0x7f04001e

.field public static final actionModeSelectAllDrawable:I = 0x7f04001f

.field public static final actionModeShareDrawable:I = 0x7f040020

.field public static final actionModeSplitBackground:I = 0x7f040021

.field public static final actionModeStyle:I = 0x7f040022

.field public static final actionModeWebSearchDrawable:I = 0x7f040024

.field public static final actionOverflowButtonStyle:I = 0x7f040025

.field public static final actionOverflowMenuStyle:I = 0x7f040026

.field public static final actionProviderClass:I = 0x7f040027

.field public static final actionViewClass:I = 0x7f040029

.field public static final activityChooserViewStyle:I = 0x7f04002b

.field public static final alertDialogButtonGroupStyle:I = 0x7f04002e

.field public static final alertDialogCenterButtons:I = 0x7f04002f

.field public static final alertDialogStyle:I = 0x7f040030

.field public static final alertDialogTheme:I = 0x7f040031

.field public static final allowStacking:I = 0x7f040035

.field public static final alpha:I = 0x7f040036

.field public static final alphabeticModifiers:I = 0x7f040037

.field public static final arrowHeadLength:I = 0x7f040041

.field public static final arrowShaftLength:I = 0x7f040042

.field public static final autoCompleteTextViewStyle:I = 0x7f040045

.field public static final autoSizeMaxTextSize:I = 0x7f040046

.field public static final autoSizeMinTextSize:I = 0x7f040047

.field public static final autoSizePresetSizes:I = 0x7f040048

.field public static final autoSizeStepGranularity:I = 0x7f040049

.field public static final autoSizeTextType:I = 0x7f04004a

.field public static final background:I = 0x7f04004c

.field public static final backgroundSplit:I = 0x7f040053

.field public static final backgroundStacked:I = 0x7f040054

.field public static final backgroundTint:I = 0x7f040055

.field public static final backgroundTintMode:I = 0x7f040056

.field public static final barLength:I = 0x7f04005d

.field public static final borderlessButtonStyle:I = 0x7f040070

.field public static final buttonBarButtonStyle:I = 0x7f040082

.field public static final buttonBarNegativeButtonStyle:I = 0x7f040083

.field public static final buttonBarNeutralButtonStyle:I = 0x7f040084

.field public static final buttonBarPositiveButtonStyle:I = 0x7f040085

.field public static final buttonBarStyle:I = 0x7f040086

.field public static final buttonGravity:I = 0x7f040088

.field public static final buttonIconDimen:I = 0x7f040089

.field public static final buttonPanelSideLayout:I = 0x7f04008a

.field public static final buttonStyle:I = 0x7f04008c

.field public static final buttonStyleSmall:I = 0x7f04008d

.field public static final buttonTint:I = 0x7f04008e

.field public static final buttonTintMode:I = 0x7f04008f

.field public static final checkboxStyle:I = 0x7f0400a7

.field public static final checkedTextViewStyle:I = 0x7f0400b0

.field public static final closeIcon:I = 0x7f0400d4

.field public static final closeItemLayout:I = 0x7f0400db

.field public static final collapseContentDescription:I = 0x7f0400dc

.field public static final collapseIcon:I = 0x7f0400dd

.field public static final color:I = 0x7f0400e7

.field public static final colorAccent:I = 0x7f0400e8

.field public static final colorBackgroundFloating:I = 0x7f0400e9

.field public static final colorButtonNormal:I = 0x7f0400ea

.field public static final colorControlActivated:I = 0x7f0400ec

.field public static final colorControlHighlight:I = 0x7f0400ed

.field public static final colorControlNormal:I = 0x7f0400ee

.field public static final colorError:I = 0x7f0400ef

.field public static final colorPrimary:I = 0x7f040100

.field public static final colorPrimaryDark:I = 0x7f040102

.field public static final colorSwitchThumbNormal:I = 0x7f04010d

.field public static final commitIcon:I = 0x7f040110

.field public static final contentDescription:I = 0x7f040119

.field public static final contentInsetEnd:I = 0x7f04011a

.field public static final contentInsetEndWithActions:I = 0x7f04011b

.field public static final contentInsetLeft:I = 0x7f04011c

.field public static final contentInsetRight:I = 0x7f04011d

.field public static final contentInsetStart:I = 0x7f04011e

.field public static final contentInsetStartWithNavigation:I = 0x7f04011f

.field public static final controlBackground:I = 0x7f040129

.field public static final coordinatorLayoutStyle:I = 0x7f04012a

.field public static final customNavigationLayout:I = 0x7f040145

.field public static final defaultQueryHint:I = 0x7f040151

.field public static final dialogCornerRadius:I = 0x7f040159

.field public static final dialogPreferredPadding:I = 0x7f04015e

.field public static final dialogTheme:I = 0x7f04015f

.field public static final displayOptions:I = 0x7f040162

.field public static final divider:I = 0x7f040163

.field public static final dividerHorizontal:I = 0x7f040165

.field public static final dividerPadding:I = 0x7f040168

.field public static final dividerVertical:I = 0x7f04016a

.field public static final drawableSize:I = 0x7f040173

.field public static final drawerArrowStyle:I = 0x7f040178

.field public static final dropDownListViewStyle:I = 0x7f04017b

.field public static final dropdownListPreferredItemHeight:I = 0x7f04017c

.field public static final editTextBackground:I = 0x7f040180

.field public static final editTextColor:I = 0x7f040181

.field public static final editTextStyle:I = 0x7f040183

.field public static final elevation:I = 0x7f040184

.field public static final expandActivityOverflowButtonDrawable:I = 0x7f0401a0

.field public static final firstBaselineToTopHeight:I = 0x7f0401c0

.field public static final font:I = 0x7f0401de

.field public static final fontFamily:I = 0x7f0401df

.field public static final fontProviderAuthority:I = 0x7f0401e0

.field public static final fontProviderCerts:I = 0x7f0401e1

.field public static final fontProviderFetchStrategy:I = 0x7f0401e2

.field public static final fontProviderFetchTimeout:I = 0x7f0401e3

.field public static final fontProviderPackage:I = 0x7f0401e4

.field public static final fontProviderQuery:I = 0x7f0401e5

.field public static final fontStyle:I = 0x7f0401e7

.field public static final fontVariationSettings:I = 0x7f0401e8

.field public static final fontWeight:I = 0x7f0401e9

.field public static final gapBetweenBars:I = 0x7f0401f0

.field public static final goIcon:I = 0x7f0401f2

.field public static final height:I = 0x7f0401fa

.field public static final hideOnContentScroll:I = 0x7f040201

.field public static final homeAsUpIndicator:I = 0x7f040207

.field public static final homeLayout:I = 0x7f040208

.field public static final icon:I = 0x7f04020c

.field public static final iconTint:I = 0x7f040213

.field public static final iconTintMode:I = 0x7f040214

.field public static final iconifiedByDefault:I = 0x7f040215

.field public static final imageButtonStyle:I = 0x7f04021a

.field public static final indeterminateProgressStyle:I = 0x7f040220

.field public static final initialActivityCount:I = 0x7f040226

.field public static final isLightTheme:I = 0x7f040229

.field public static final itemPadding:I = 0x7f040237

.field public static final keylines:I = 0x7f04024d

.field public static final lastBaselineToBottomHeight:I = 0x7f040252

.field public static final layout:I = 0x7f040254

.field public static final layout_anchor:I = 0x7f040258

.field public static final layout_anchorGravity:I = 0x7f040259

.field public static final layout_behavior:I = 0x7f04025a

.field public static final layout_dodgeInsetEdges:I = 0x7f04028b

.field public static final layout_insetEdge:I = 0x7f040295

.field public static final layout_keyline:I = 0x7f040296

.field public static final lineHeight:I = 0x7f0402a0

.field public static final listChoiceBackgroundIndicator:I = 0x7f0402a3

.field public static final listDividerAlertDialog:I = 0x7f0402a6

.field public static final listItemLayout:I = 0x7f0402a7

.field public static final listLayout:I = 0x7f0402a8

.field public static final listMenuViewStyle:I = 0x7f0402a9

.field public static final listPopupWindowStyle:I = 0x7f0402aa

.field public static final listPreferredItemHeight:I = 0x7f0402ab

.field public static final listPreferredItemHeightLarge:I = 0x7f0402ac

.field public static final listPreferredItemHeightSmall:I = 0x7f0402ad

.field public static final listPreferredItemPaddingLeft:I = 0x7f0402af

.field public static final listPreferredItemPaddingRight:I = 0x7f0402b0

.field public static final logo:I = 0x7f0402b2

.field public static final logoDescription:I = 0x7f0402b3

.field public static final maxButtonHeight:I = 0x7f0402dc

.field public static final measureWithLargestChild:I = 0x7f0402e3

.field public static final multiChoiceItemLayout:I = 0x7f040312

.field public static final navigationContentDescription:I = 0x7f040314

.field public static final navigationIcon:I = 0x7f040315

.field public static final navigationMode:I = 0x7f040317

.field public static final numericModifiers:I = 0x7f040320

.field public static final overlapAnchor:I = 0x7f04032a

.field public static final paddingBottomNoButtons:I = 0x7f04032c

.field public static final paddingEnd:I = 0x7f04032e

.field public static final paddingStart:I = 0x7f040331

.field public static final paddingTopNoTitle:I = 0x7f040332

.field public static final panelBackground:I = 0x7f040334

.field public static final panelMenuListTheme:I = 0x7f040335

.field public static final panelMenuListWidth:I = 0x7f040336

.field public static final popupMenuStyle:I = 0x7f040351

.field public static final popupTheme:I = 0x7f040352

.field public static final popupWindowStyle:I = 0x7f040353

.field public static final preserveIconSpacing:I = 0x7f040361

.field public static final progressBarPadding:I = 0x7f040364

.field public static final progressBarStyle:I = 0x7f040365

.field public static final queryBackground:I = 0x7f04036a

.field public static final queryHint:I = 0x7f04036b

.field public static final radioButtonStyle:I = 0x7f04036d

.field public static final ratingBarStyle:I = 0x7f04036f

.field public static final ratingBarStyleIndicator:I = 0x7f040370

.field public static final ratingBarStyleSmall:I = 0x7f040371

.field public static final searchHintIcon:I = 0x7f04038d

.field public static final searchIcon:I = 0x7f04038e

.field public static final searchViewStyle:I = 0x7f04038f

.field public static final seekBarStyle:I = 0x7f040394

.field public static final selectableItemBackground:I = 0x7f040396

.field public static final selectableItemBackgroundBorderless:I = 0x7f040397

.field public static final showAsAction:I = 0x7f0403a3

.field public static final showDividers:I = 0x7f0403a5

.field public static final showText:I = 0x7f0403a9

.field public static final showTitle:I = 0x7f0403aa

.field public static final singleChoiceItemLayout:I = 0x7f0403ac

.field public static final spinBars:I = 0x7f0403b6

.field public static final spinnerDropDownItemStyle:I = 0x7f0403b7

.field public static final spinnerStyle:I = 0x7f0403b8

.field public static final splitTrack:I = 0x7f0403bd

.field public static final srcCompat:I = 0x7f0403c3

.field public static final state_above_anchor:I = 0x7f0403cc

.field public static final statusBarBackground:I = 0x7f0403d2

.field public static final subMenuArrow:I = 0x7f0403d7

.field public static final submitBackground:I = 0x7f0403dc

.field public static final subtitle:I = 0x7f0403dd

.field public static final subtitleTextAppearance:I = 0x7f0403df

.field public static final subtitleTextColor:I = 0x7f0403e0

.field public static final subtitleTextStyle:I = 0x7f0403e1

.field public static final suggestionRowLayout:I = 0x7f0403e5

.field public static final switchMinWidth:I = 0x7f040423

.field public static final switchPadding:I = 0x7f040424

.field public static final switchStyle:I = 0x7f040427

.field public static final switchTextAppearance:I = 0x7f040428

.field public static final textAllCaps:I = 0x7f04044c

.field public static final textAppearanceLargePopupMenu:I = 0x7f040463

.field public static final textAppearanceListItem:I = 0x7f040465

.field public static final textAppearanceListItemSecondary:I = 0x7f040466

.field public static final textAppearanceListItemSmall:I = 0x7f040467

.field public static final textAppearancePopupMenuHeader:I = 0x7f040469

.field public static final textAppearanceSearchResultSubtitle:I = 0x7f04046a

.field public static final textAppearanceSearchResultTitle:I = 0x7f04046b

.field public static final textAppearanceSmallPopupMenu:I = 0x7f04046c

.field public static final textColorAlertDialogListItem:I = 0x7f040477

.field public static final textColorSearchUrl:I = 0x7f040478

.field public static final theme:I = 0x7f04048d

.field public static final thickness:I = 0x7f04048f

.field public static final thumbTextPadding:I = 0x7f040495

.field public static final thumbTint:I = 0x7f040496

.field public static final thumbTintMode:I = 0x7f040497

.field public static final tickMark:I = 0x7f04049b

.field public static final tickMarkTint:I = 0x7f04049c

.field public static final tickMarkTintMode:I = 0x7f04049d

.field public static final tint:I = 0x7f04049f

.field public static final tintMode:I = 0x7f0404a0

.field public static final title:I = 0x7f0404a1

.field public static final titleMargin:I = 0x7f0404a5

.field public static final titleMarginBottom:I = 0x7f0404a6

.field public static final titleMarginEnd:I = 0x7f0404a7

.field public static final titleMarginStart:I = 0x7f0404a8

.field public static final titleMarginTop:I = 0x7f0404a9

.field public static final titleMargins:I = 0x7f0404aa

.field public static final titleTextAppearance:I = 0x7f0404ac

.field public static final titleTextColor:I = 0x7f0404ad

.field public static final titleTextStyle:I = 0x7f0404ae

.field public static final toolbarNavigationButtonStyle:I = 0x7f0404b7

.field public static final toolbarStyle:I = 0x7f0404b8

.field public static final tooltipForegroundColor:I = 0x7f0404ba

.field public static final tooltipFrameBackground:I = 0x7f0404bb

.field public static final tooltipText:I = 0x7f0404bd

.field public static final track:I = 0x7f0404c2

.field public static final trackTint:I = 0x7f0404c9

.field public static final trackTintMode:I = 0x7f0404ca

.field public static final ttcIndex:I = 0x7f0404d4

.field public static final viewInflaterClass:I = 0x7f0404e4

.field public static final voiceIcon:I = 0x7f0404ea

.field public static final windowActionBar:I = 0x7f0404f3

.field public static final windowActionBarOverlay:I = 0x7f0404f4

.field public static final windowActionModeOverlay:I = 0x7f0404f5

.field public static final windowFixedHeightMajor:I = 0x7f0404f6

.field public static final windowFixedHeightMinor:I = 0x7f0404f7

.field public static final windowFixedWidthMajor:I = 0x7f0404f8

.field public static final windowFixedWidthMinor:I = 0x7f0404f9

.field public static final windowMinWidthMajor:I = 0x7f0404fa

.field public static final windowMinWidthMinor:I = 0x7f0404fb

.field public static final windowNoTitle:I = 0x7f0404fc


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
