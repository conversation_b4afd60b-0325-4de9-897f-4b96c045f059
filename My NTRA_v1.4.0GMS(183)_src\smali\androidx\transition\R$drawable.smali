.class public final Landroidx/transition/R$drawable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "drawable"
.end annotation


# static fields
.field public static final abc_ab_share_pack_mtrl_alpha:I = 0x7f080074

.field public static final abc_action_bar_item_background_material:I = 0x7f080075

.field public static final abc_btn_borderless_material:I = 0x7f080076

.field public static final abc_btn_check_material:I = 0x7f080077

.field public static final abc_btn_check_to_on_mtrl_000:I = 0x7f080079

.field public static final abc_btn_check_to_on_mtrl_015:I = 0x7f08007a

.field public static final abc_btn_colored_material:I = 0x7f08007b

.field public static final abc_btn_default_mtrl_shape:I = 0x7f08007c

.field public static final abc_btn_radio_material:I = 0x7f08007d

.field public static final abc_btn_radio_to_on_mtrl_000:I = 0x7f08007f

.field public static final abc_btn_radio_to_on_mtrl_015:I = 0x7f080080

.field public static final abc_btn_switch_to_on_mtrl_00001:I = 0x7f080081

.field public static final abc_btn_switch_to_on_mtrl_00012:I = 0x7f080082

.field public static final abc_cab_background_internal_bg:I = 0x7f080083

.field public static final abc_cab_background_top_material:I = 0x7f080084

.field public static final abc_cab_background_top_mtrl_alpha:I = 0x7f080085

.field public static final abc_control_background_material:I = 0x7f080086

.field public static final abc_dialog_material_background:I = 0x7f080087

.field public static final abc_edit_text_material:I = 0x7f080088

.field public static final abc_ic_ab_back_material:I = 0x7f080089

.field public static final abc_ic_arrow_drop_right_black_24dp:I = 0x7f08008a

.field public static final abc_ic_clear_material:I = 0x7f08008b

.field public static final abc_ic_commit_search_api_mtrl_alpha:I = 0x7f08008c

.field public static final abc_ic_go_search_api_material:I = 0x7f08008d

.field public static final abc_ic_menu_copy_mtrl_am_alpha:I = 0x7f08008e

.field public static final abc_ic_menu_cut_mtrl_alpha:I = 0x7f08008f

.field public static final abc_ic_menu_overflow_material:I = 0x7f080090

.field public static final abc_ic_menu_paste_mtrl_am_alpha:I = 0x7f080091

.field public static final abc_ic_menu_selectall_mtrl_alpha:I = 0x7f080092

.field public static final abc_ic_menu_share_mtrl_alpha:I = 0x7f080093

.field public static final abc_ic_search_api_material:I = 0x7f080094

.field public static final abc_ic_voice_search_api_material:I = 0x7f080095

.field public static final abc_item_background_holo_dark:I = 0x7f080096

.field public static final abc_item_background_holo_light:I = 0x7f080097

.field public static final abc_list_divider_material:I = 0x7f080098

.field public static final abc_list_divider_mtrl_alpha:I = 0x7f080099

.field public static final abc_list_focused_holo:I = 0x7f08009a

.field public static final abc_list_longpressed_holo:I = 0x7f08009b

.field public static final abc_list_pressed_holo_dark:I = 0x7f08009c

.field public static final abc_list_pressed_holo_light:I = 0x7f08009d

.field public static final abc_list_selector_background_transition_holo_dark:I = 0x7f08009e

.field public static final abc_list_selector_background_transition_holo_light:I = 0x7f08009f

.field public static final abc_list_selector_disabled_holo_dark:I = 0x7f0800a0

.field public static final abc_list_selector_disabled_holo_light:I = 0x7f0800a1

.field public static final abc_list_selector_holo_dark:I = 0x7f0800a2

.field public static final abc_list_selector_holo_light:I = 0x7f0800a3

.field public static final abc_menu_hardkey_panel_mtrl_mult:I = 0x7f0800a4

.field public static final abc_popup_background_mtrl_mult:I = 0x7f0800a5

.field public static final abc_ratingbar_indicator_material:I = 0x7f0800a6

.field public static final abc_ratingbar_material:I = 0x7f0800a7

.field public static final abc_ratingbar_small_material:I = 0x7f0800a8

.field public static final abc_scrubber_control_off_mtrl_alpha:I = 0x7f0800a9

.field public static final abc_scrubber_control_to_pressed_mtrl_000:I = 0x7f0800aa

.field public static final abc_scrubber_control_to_pressed_mtrl_005:I = 0x7f0800ab

.field public static final abc_scrubber_primary_mtrl_alpha:I = 0x7f0800ac

.field public static final abc_scrubber_track_mtrl_alpha:I = 0x7f0800ad

.field public static final abc_seekbar_thumb_material:I = 0x7f0800ae

.field public static final abc_seekbar_tick_mark_material:I = 0x7f0800af

.field public static final abc_seekbar_track_material:I = 0x7f0800b0

.field public static final abc_spinner_mtrl_am_alpha:I = 0x7f0800b1

.field public static final abc_spinner_textfield_background_material:I = 0x7f0800b2

.field public static final abc_switch_thumb_material:I = 0x7f0800b5

.field public static final abc_switch_track_mtrl_alpha:I = 0x7f0800b6

.field public static final abc_tab_indicator_material:I = 0x7f0800b7

.field public static final abc_tab_indicator_mtrl_alpha:I = 0x7f0800b8

.field public static final abc_text_cursor_material:I = 0x7f0800b9

.field public static final abc_textfield_activated_mtrl_alpha:I = 0x7f0800bd

.field public static final abc_textfield_default_mtrl_alpha:I = 0x7f0800be

.field public static final abc_textfield_search_activated_mtrl_alpha:I = 0x7f0800bf

.field public static final abc_textfield_search_default_mtrl_alpha:I = 0x7f0800c0

.field public static final abc_textfield_search_material:I = 0x7f0800c1

.field public static final abc_vector_test:I = 0x7f0800c2

.field public static final notification_action_background:I = 0x7f0801e4

.field public static final notification_bg:I = 0x7f0801e5

.field public static final notification_bg_low:I = 0x7f0801e6

.field public static final notification_bg_low_normal:I = 0x7f0801e7

.field public static final notification_bg_low_pressed:I = 0x7f0801e8

.field public static final notification_bg_normal:I = 0x7f0801e9

.field public static final notification_bg_normal_pressed:I = 0x7f0801ea

.field public static final notification_icon_background:I = 0x7f0801eb

.field public static final notification_template_icon_bg:I = 0x7f0801ed

.field public static final notification_template_icon_low_bg:I = 0x7f0801ee

.field public static final notification_tile_bg:I = 0x7f0801ef

.field public static final notify_panel_notification_icon_bg:I = 0x7f0801f0

.field public static final tooltip_frame_dark:I = 0x7f0801ff

.field public static final tooltip_frame_light:I = 0x7f080200


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
