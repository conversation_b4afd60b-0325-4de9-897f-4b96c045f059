.class public final Landroidx/transition/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static final accessibility_action_clickable_span:I = 0x7f0a0041

.field public static final accessibility_custom_action_0:I = 0x7f0a0042

.field public static final accessibility_custom_action_1:I = 0x7f0a0043

.field public static final accessibility_custom_action_10:I = 0x7f0a0044

.field public static final accessibility_custom_action_11:I = 0x7f0a0045

.field public static final accessibility_custom_action_12:I = 0x7f0a0046

.field public static final accessibility_custom_action_13:I = 0x7f0a0047

.field public static final accessibility_custom_action_14:I = 0x7f0a0048

.field public static final accessibility_custom_action_15:I = 0x7f0a0049

.field public static final accessibility_custom_action_16:I = 0x7f0a004a

.field public static final accessibility_custom_action_17:I = 0x7f0a004b

.field public static final accessibility_custom_action_18:I = 0x7f0a004c

.field public static final accessibility_custom_action_19:I = 0x7f0a004d

.field public static final accessibility_custom_action_2:I = 0x7f0a004e

.field public static final accessibility_custom_action_20:I = 0x7f0a004f

.field public static final accessibility_custom_action_21:I = 0x7f0a0050

.field public static final accessibility_custom_action_22:I = 0x7f0a0051

.field public static final accessibility_custom_action_23:I = 0x7f0a0052

.field public static final accessibility_custom_action_24:I = 0x7f0a0053

.field public static final accessibility_custom_action_25:I = 0x7f0a0054

.field public static final accessibility_custom_action_26:I = 0x7f0a0055

.field public static final accessibility_custom_action_27:I = 0x7f0a0056

.field public static final accessibility_custom_action_28:I = 0x7f0a0057

.field public static final accessibility_custom_action_29:I = 0x7f0a0058

.field public static final accessibility_custom_action_3:I = 0x7f0a0059

.field public static final accessibility_custom_action_30:I = 0x7f0a005a

.field public static final accessibility_custom_action_31:I = 0x7f0a005b

.field public static final accessibility_custom_action_4:I = 0x7f0a005c

.field public static final accessibility_custom_action_5:I = 0x7f0a005d

.field public static final accessibility_custom_action_6:I = 0x7f0a005e

.field public static final accessibility_custom_action_7:I = 0x7f0a005f

.field public static final accessibility_custom_action_8:I = 0x7f0a0060

.field public static final accessibility_custom_action_9:I = 0x7f0a0061

.field public static final action_bar:I = 0x7f0a006f

.field public static final action_bar_activity_content:I = 0x7f0a0070

.field public static final action_bar_container:I = 0x7f0a0071

.field public static final action_bar_root:I = 0x7f0a0072

.field public static final action_bar_spinner:I = 0x7f0a0073

.field public static final action_bar_subtitle:I = 0x7f0a0074

.field public static final action_bar_title:I = 0x7f0a0075

.field public static final action_container:I = 0x7f0a0081

.field public static final action_context_bar:I = 0x7f0a0082

.field public static final action_divider:I = 0x7f0a0089

.field public static final action_image:I = 0x7f0a0096

.field public static final action_menu_divider:I = 0x7f0a009f

.field public static final action_menu_presenter:I = 0x7f0a00a0

.field public static final action_mode_bar:I = 0x7f0a00a1

.field public static final action_mode_bar_stub:I = 0x7f0a00a2

.field public static final action_mode_close_button:I = 0x7f0a00a3

.field public static final action_text:I = 0x7f0a00c1

.field public static final actions:I = 0x7f0a00cc

.field public static final activity_chooser_view_content:I = 0x7f0a00cd

.field public static final add:I = 0x7f0a00ce

.field public static final alertTitle:I = 0x7f0a00d1

.field public static final async:I = 0x7f0a00e1

.field public static final blocking:I = 0x7f0a00eb

.field public static final bottom:I = 0x7f0a00ec

.field public static final buttonPanel:I = 0x7f0a010d

.field public static final checkbox:I = 0x7f0a011c

.field public static final chronometer:I = 0x7f0a0123

.field public static final content:I = 0x7f0a0144

.field public static final contentPanel:I = 0x7f0a0145

.field public static final custom:I = 0x7f0a014f

.field public static final customPanel:I = 0x7f0a0150

.field public static final decor_content_parent:I = 0x7f0a015f

.field public static final default_activity_button:I = 0x7f0a0160

.field public static final dialog_button:I = 0x7f0a016a

.field public static final edit_query:I = 0x7f0a0181

.field public static final end:I = 0x7f0a0184

.field public static final expand_activities_button:I = 0x7f0a01a9

.field public static final expanded_menu:I = 0x7f0a01aa

.field public static final forever:I = 0x7f0a01b7

.field public static final fragment_container_view_tag:I = 0x7f0a01b9

.field public static final ghost_view:I = 0x7f0a01bf

.field public static final ghost_view_holder:I = 0x7f0a01c0

.field public static final group_divider:I = 0x7f0a01ca

.field public static final home:I = 0x7f0a01da

.field public static final icon:I = 0x7f0a01e1

.field public static final icon_group:I = 0x7f0a01e3

.field public static final image:I = 0x7f0a01e8

.field public static final info:I = 0x7f0a01ee

.field public static final italic:I = 0x7f0a01f3

.field public static final left:I = 0x7f0a0216

.field public static final line1:I = 0x7f0a021a

.field public static final line3:I = 0x7f0a021b

.field public static final listMode:I = 0x7f0a021e

.field public static final list_item:I = 0x7f0a021f

.field public static final message:I = 0x7f0a0271

.field public static final multiply:I = 0x7f0a0293

.field public static final none:I = 0x7f0a02b4

.field public static final normal:I = 0x7f0a02b5

.field public static final notification_background:I = 0x7f0a02b7

.field public static final notification_main_column:I = 0x7f0a02b8

.field public static final notification_main_column_container:I = 0x7f0a02b9

.field public static final parentPanel:I = 0x7f0a02cd

.field public static final parent_matrix:I = 0x7f0a02d1

.field public static final progress_circular:I = 0x7f0a02e8

.field public static final progress_horizontal:I = 0x7f0a02e9

.field public static final radio:I = 0x7f0a02ec

.field public static final right:I = 0x7f0a02fc

.field public static final right_icon:I = 0x7f0a02fe

.field public static final right_side:I = 0x7f0a02ff

.field public static final save_non_transition_alpha:I = 0x7f0a0316

.field public static final save_overlay_view:I = 0x7f0a0317

.field public static final screen:I = 0x7f0a031a

.field public static final scrollIndicatorDown:I = 0x7f0a031c

.field public static final scrollIndicatorUp:I = 0x7f0a031d

.field public static final scrollView:I = 0x7f0a031e

.field public static final search_badge:I = 0x7f0a0321

.field public static final search_bar:I = 0x7f0a0322

.field public static final search_button:I = 0x7f0a0323

.field public static final search_close_btn:I = 0x7f0a0324

.field public static final search_edit_frame:I = 0x7f0a0325

.field public static final search_go_btn:I = 0x7f0a0326

.field public static final search_mag_icon:I = 0x7f0a0327

.field public static final search_plate:I = 0x7f0a0328

.field public static final search_src_text:I = 0x7f0a0329

.field public static final search_voice_btn:I = 0x7f0a032a

.field public static final select_dialog_listview:I = 0x7f0a032d

.field public static final shortcut:I = 0x7f0a0336

.field public static final spacer:I = 0x7f0a0351

.field public static final split_action_bar:I = 0x7f0a0358

.field public static final src_atop:I = 0x7f0a0360

.field public static final src_in:I = 0x7f0a0361

.field public static final src_over:I = 0x7f0a0362

.field public static final start:I = 0x7f0a0364

.field public static final submenuarrow:I = 0x7f0a0373

.field public static final submit_area:I = 0x7f0a0374

.field public static final tabMode:I = 0x7f0a037d

.field public static final tag_accessibility_actions:I = 0x7f0a037e

.field public static final tag_accessibility_clickable_spans:I = 0x7f0a037f

.field public static final tag_accessibility_heading:I = 0x7f0a0380

.field public static final tag_accessibility_pane_title:I = 0x7f0a0381

.field public static final tag_screen_reader_focusable:I = 0x7f0a0385

.field public static final tag_transition_group:I = 0x7f0a0387

.field public static final tag_unhandled_key_event_manager:I = 0x7f0a0388

.field public static final tag_unhandled_key_listeners:I = 0x7f0a0389

.field public static final text:I = 0x7f0a0393

.field public static final text2:I = 0x7f0a0394

.field public static final textSpacerNoButtons:I = 0x7f0a0399

.field public static final textSpacerNoTitle:I = 0x7f0a039a

.field public static final time:I = 0x7f0a03c5

.field public static final title:I = 0x7f0a03c6

.field public static final titleDividerNoCustom:I = 0x7f0a03c7

.field public static final title_template:I = 0x7f0a03c8

.field public static final top:I = 0x7f0a03cb

.field public static final topPanel:I = 0x7f0a03cc

.field public static final transition_current_scene:I = 0x7f0a03d0

.field public static final transition_layout_save:I = 0x7f0a03d1

.field public static final transition_position:I = 0x7f0a03d2

.field public static final transition_scene_layoutid_cache:I = 0x7f0a03d3

.field public static final transition_transform:I = 0x7f0a03d4

.field public static final uniform:I = 0x7f0a043c

.field public static final up:I = 0x7f0a043e

.field public static final visible_removing_fragment_view_tag:I = 0x7f0a0451

.field public static final wrap_content:I = 0x7f0a0458


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
