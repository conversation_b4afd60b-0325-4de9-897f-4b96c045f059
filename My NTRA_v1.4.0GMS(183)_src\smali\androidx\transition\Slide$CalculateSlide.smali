.class interface abstract Landroidx/transition/Slide$CalculateSlide;
.super Ljava/lang/Object;
.source "Slide.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/Slide;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x60a
    name = "CalculateSlide"
.end annotation


# virtual methods
.method public abstract getGoneX(Landroid/view/ViewGroup;Landroid/view/View;)F
.end method

.method public abstract getGoneY(Landroid/view/ViewGroup;Landroid/view/View;)F
.end method
