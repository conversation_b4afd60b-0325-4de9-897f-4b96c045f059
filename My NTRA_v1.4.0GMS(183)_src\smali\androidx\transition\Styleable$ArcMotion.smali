.class interface abstract Landroidx/transition/Styleable$ArcMotion;
.super Ljava/lang/Object;
.source "Styleable.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/Styleable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "ArcMotion"
.end annotation


# static fields
.field public static final MAXIMUM_ANGLE:I = 0x2

.field public static final MINIMUM_HORIZONTAL_ANGLE:I = 0x0

.field public static final MINIMUM_VERTICAL_ANGLE:I = 0x1
