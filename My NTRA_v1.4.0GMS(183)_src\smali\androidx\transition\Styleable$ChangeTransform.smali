.class interface abstract Landroidx/transition/Styleable$ChangeTransform;
.super Ljava/lang/Object;
.source "Styleable.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/Styleable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "ChangeTransform"
.end annotation


# static fields
.field public static final REPARENT:I = 0x0

.field public static final REPARENT_WITH_OVERLAY:I = 0x1
