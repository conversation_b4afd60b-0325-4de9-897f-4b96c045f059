.class interface abstract Landroidx/transition/Styleable$TransitionManager;
.super Ljava/lang/Object;
.source "Styleable.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/Styleable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "TransitionManager"
.end annotation


# static fields
.field public static final FROM_SCENE:I = 0x0

.field public static final TO_SCENE:I = 0x1

.field public static final TRANSITION:I = 0x2
