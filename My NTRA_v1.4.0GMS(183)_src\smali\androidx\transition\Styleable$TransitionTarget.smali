.class interface abstract Landroidx/transition/Styleable$TransitionTarget;
.super Ljava/lang/Object;
.source "Styleable.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/transition/Styleable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "TransitionTarget"
.end annotation


# static fields
.field public static final EXCLUDE_CLASS:I = 0x3

.field public static final EXCLUDE_ID:I = 0x2

.field public static final EXCLUDE_NAME:I = 0x5

.field public static final TARGET_CLASS:I = 0x0

.field public static final TARGET_ID:I = 0x1

.field public static final TARGET_NAME:I = 0x4
