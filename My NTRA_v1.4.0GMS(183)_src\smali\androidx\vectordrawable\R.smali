.class public final Landroidx/vectordrawable/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/vectordrawable/R$attr;,
        Landroidx/vectordrawable/R$color;,
        Landroidx/vectordrawable/R$dimen;,
        Landroidx/vectordrawable/R$drawable;,
        Landroidx/vectordrawable/R$id;,
        Landroidx/vectordrawable/R$integer;,
        Landroidx/vectordrawable/R$layout;,
        Landroidx/vectordrawable/R$string;,
        Landroidx/vectordrawable/R$style;,
        Landroidx/vectordrawable/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
