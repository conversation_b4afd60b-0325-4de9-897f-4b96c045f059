.class public final Landroidx/vectordrawable/animated/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/vectordrawable/animated/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static final alpha:I = 0x7f040036

.field public static final font:I = 0x7f0401de

.field public static final fontProviderAuthority:I = 0x7f0401e0

.field public static final fontProviderCerts:I = 0x7f0401e1

.field public static final fontProviderFetchStrategy:I = 0x7f0401e2

.field public static final fontProviderFetchTimeout:I = 0x7f0401e3

.field public static final fontProviderPackage:I = 0x7f0401e4

.field public static final fontProviderQuery:I = 0x7f0401e5

.field public static final fontStyle:I = 0x7f0401e7

.field public static final fontVariationSettings:I = 0x7f0401e8

.field public static final fontWeight:I = 0x7f0401e9

.field public static final ttcIndex:I = 0x7f0404d4


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
