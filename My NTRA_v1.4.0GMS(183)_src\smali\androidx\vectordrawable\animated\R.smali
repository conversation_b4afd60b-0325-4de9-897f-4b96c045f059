.class public final Landroidx/vectordrawable/animated/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/vectordrawable/animated/R$attr;,
        Landroidx/vectordrawable/animated/R$color;,
        Landroidx/vectordrawable/animated/R$dimen;,
        Landroidx/vectordrawable/animated/R$drawable;,
        Landroidx/vectordrawable/animated/R$id;,
        Landroidx/vectordrawable/animated/R$integer;,
        Landroidx/vectordrawable/animated/R$layout;,
        Landroidx/vectordrawable/animated/R$string;,
        Landroidx/vectordrawable/animated/R$style;,
        Landroidx/vectordrawable/animated/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
