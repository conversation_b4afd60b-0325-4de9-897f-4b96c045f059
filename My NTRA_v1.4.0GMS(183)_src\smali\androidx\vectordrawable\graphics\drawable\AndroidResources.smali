.class Landroidx/vectordrawable/graphics/drawable/AndroidResources;
.super Ljava/lang/Object;
.source "AndroidResources.java"


# static fields
.field public static final FAST_OUT_LINEAR_IN:I = 0x10c000f

.field public static final FAST_OUT_SLOW_IN:I = 0x10c000d

.field public static final LINEAR_OUT_SLOW_IN:I = 0x10c000e

.field static final STYLEABLE_ANIMATED_VECTOR_DRAWABLE:[I

.field static final STYLEABLE_ANIMATED_VECTOR_DRAWABLE_DRAWABLE:I = 0x0

.field static final STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET:[I

.field static final STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET_ANIMATION:I = 0x1

.field static final STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET_NAME:I = 0x0

.field public static final STYLEABLE_ANIMATOR:[I

.field public static final STYLEABLE_ANIMATOR_DURATION:I = 0x1

.field public static final STYLEABLE_ANIMATOR_INTERPOLATOR:I = 0x0

.field public static final STYLEABLE_ANIMATOR_REPEAT_COUNT:I = 0x3

.field public static final STYLEABLE_ANIMATOR_REPEAT_MODE:I = 0x4

.field public static final STYLEABLE_ANIMATOR_SET:[I

.field public static final STYLEABLE_ANIMATOR_SET_ORDERING:I = 0x0

.field public static final STYLEABLE_ANIMATOR_START_OFFSET:I = 0x2

.field public static final STYLEABLE_ANIMATOR_VALUE_FROM:I = 0x5

.field public static final STYLEABLE_ANIMATOR_VALUE_TO:I = 0x6

.field public static final STYLEABLE_ANIMATOR_VALUE_TYPE:I = 0x7

.field public static final STYLEABLE_KEYFRAME:[I

.field public static final STYLEABLE_KEYFRAME_FRACTION:I = 0x3

.field public static final STYLEABLE_KEYFRAME_INTERPOLATOR:I = 0x1

.field public static final STYLEABLE_KEYFRAME_VALUE:I = 0x0

.field public static final STYLEABLE_KEYFRAME_VALUE_TYPE:I = 0x2

.field public static final STYLEABLE_PATH_INTERPOLATOR:[I

.field public static final STYLEABLE_PATH_INTERPOLATOR_CONTROL_X_1:I = 0x0

.field public static final STYLEABLE_PATH_INTERPOLATOR_CONTROL_X_2:I = 0x2

.field public static final STYLEABLE_PATH_INTERPOLATOR_CONTROL_Y_1:I = 0x1

.field public static final STYLEABLE_PATH_INTERPOLATOR_CONTROL_Y_2:I = 0x3

.field public static final STYLEABLE_PATH_INTERPOLATOR_PATH_DATA:I = 0x4

.field public static final STYLEABLE_PROPERTY_ANIMATOR:[I

.field public static final STYLEABLE_PROPERTY_ANIMATOR_PATH_DATA:I = 0x1

.field public static final STYLEABLE_PROPERTY_ANIMATOR_PROPERTY_NAME:I = 0x0

.field public static final STYLEABLE_PROPERTY_ANIMATOR_PROPERTY_X_NAME:I = 0x2

.field public static final STYLEABLE_PROPERTY_ANIMATOR_PROPERTY_Y_NAME:I = 0x3

.field public static final STYLEABLE_PROPERTY_VALUES_HOLDER:[I

.field public static final STYLEABLE_PROPERTY_VALUES_HOLDER_PROPERTY_NAME:I = 0x3

.field public static final STYLEABLE_PROPERTY_VALUES_HOLDER_VALUE_FROM:I = 0x0

.field public static final STYLEABLE_PROPERTY_VALUES_HOLDER_VALUE_TO:I = 0x1

.field public static final STYLEABLE_PROPERTY_VALUES_HOLDER_VALUE_TYPE:I = 0x2

.field static final STYLEABLE_VECTOR_DRAWABLE_ALPHA:I = 0x4

.field static final STYLEABLE_VECTOR_DRAWABLE_AUTO_MIRRORED:I = 0x5

.field static final STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH:[I

.field static final STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH_FILLTYPE:I = 0x2

.field static final STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH_NAME:I = 0x0

.field static final STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH_PATH_DATA:I = 0x1

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP:[I

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_NAME:I = 0x0

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_PIVOT_X:I = 0x1

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_PIVOT_Y:I = 0x2

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_ROTATION:I = 0x5

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_SCALE_X:I = 0x3

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_SCALE_Y:I = 0x4

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_TRANSLATE_X:I = 0x6

.field static final STYLEABLE_VECTOR_DRAWABLE_GROUP_TRANSLATE_Y:I = 0x7

.field static final STYLEABLE_VECTOR_DRAWABLE_HEIGHT:I = 0x2

.field static final STYLEABLE_VECTOR_DRAWABLE_NAME:I = 0x0

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH:[I

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_FILL_ALPHA:I = 0xc

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_FILL_COLOR:I = 0x1

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_NAME:I = 0x0

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_PATH_DATA:I = 0x2

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_ALPHA:I = 0xb

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_COLOR:I = 0x3

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_LINE_CAP:I = 0x8

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_LINE_JOIN:I = 0x9

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_MITER_LIMIT:I = 0xa

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_STROKE_WIDTH:I = 0x4

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_END:I = 0x6

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_FILLTYPE:I = 0xd

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_OFFSET:I = 0x7

.field static final STYLEABLE_VECTOR_DRAWABLE_PATH_TRIM_PATH_START:I = 0x5

.field static final STYLEABLE_VECTOR_DRAWABLE_TINT:I = 0x1

.field static final STYLEABLE_VECTOR_DRAWABLE_TINT_MODE:I = 0x6

.field static final STYLEABLE_VECTOR_DRAWABLE_TYPE_ARRAY:[I

.field static final STYLEABLE_VECTOR_DRAWABLE_VIEWPORT_HEIGHT:I = 0x8

.field static final STYLEABLE_VECTOR_DRAWABLE_VIEWPORT_WIDTH:I = 0x7

.field static final STYLEABLE_VECTOR_DRAWABLE_WIDTH:I = 0x3


# direct methods
.method static constructor <clinit>()V
    .locals 6

    const/16 v0, 0x9

    new-array v0, v0, [I

    .line 22
    fill-array-data v0, :array_0

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_VECTOR_DRAWABLE_TYPE_ARRAY:[I

    const/16 v0, 0x8

    new-array v1, v0, [I

    .line 36
    fill-array-data v1, :array_1

    sput-object v1, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_VECTOR_DRAWABLE_GROUP:[I

    const/16 v1, 0xe

    new-array v1, v1, [I

    .line 49
    fill-array-data v1, :array_2

    sput-object v1, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_VECTOR_DRAWABLE_PATH:[I

    const v1, 0x101051e

    const v2, 0x1010003

    const v3, 0x1010405

    .line 70
    filled-new-array {v2, v3, v1}, [I

    move-result-object v1

    sput-object v1, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_VECTOR_DRAWABLE_CLIP_PATH:[I

    const v1, 0x1010199

    .line 77
    filled-new-array {v1}, [I

    move-result-object v1

    sput-object v1, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_ANIMATED_VECTOR_DRAWABLE:[I

    const v1, 0x10101cd

    .line 81
    filled-new-array {v2, v1}, [I

    move-result-object v1

    sput-object v1, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_ANIMATED_VECTOR_DRAWABLE_TARGET:[I

    new-array v0, v0, [I

    .line 91
    fill-array-data v0, :array_3

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_ANIMATOR:[I

    const v0, 0x10102e2

    .line 104
    filled-new-array {v0}, [I

    move-result-object v0

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_ANIMATOR_SET:[I

    const v0, 0x10102de

    const v1, 0x10102df

    const v2, 0x10102e0

    const v4, 0x10102e1

    .line 109
    filled-new-array {v0, v1, v2, v4}, [I

    move-result-object v0

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_PROPERTY_VALUES_HOLDER:[I

    const v0, 0x1010141

    const v1, 0x10104d8

    const v5, 0x1010024

    .line 117
    filled-new-array {v5, v0, v2, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_KEYFRAME:[I

    const v0, 0x1010474

    const v1, 0x1010475

    .line 125
    filled-new-array {v4, v3, v0, v1}, [I

    move-result-object v0

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_PROPERTY_ANIMATOR:[I

    const v0, 0x10103fe

    const v1, 0x10103ff

    const v2, 0x10103fc

    const v4, 0x10103fd

    .line 134
    filled-new-array {v2, v4, v0, v1, v3}, [I

    move-result-object v0

    sput-object v0, Landroidx/vectordrawable/graphics/drawable/AndroidResources;->STYLEABLE_PATH_INTERPOLATOR:[I

    return-void

    :array_0
    .array-data 4
        0x1010003
        0x1010121
        0x1010155
        0x1010159
        0x101031f
        0x10103ea
        0x10103fb
        0x1010402
        0x1010403
    .end array-data

    :array_1
    .array-data 4
        0x1010003
        0x10101b5
        0x10101b6
        0x1010324
        0x1010325
        0x1010326
        0x101045a
        0x101045b
    .end array-data

    :array_2
    .array-data 4
        0x1010003
        0x1010404
        0x1010405
        0x1010406
        0x1010407
        0x1010408
        0x1010409
        0x101040a
        0x101040b
        0x101040c
        0x101040d
        0x10104cb
        0x10104cc
        0x101051e
    .end array-data

    :array_3
    .array-data 4
        0x1010141
        0x1010198
        0x10101be
        0x10101bf
        0x10101c0
        0x10102de
        0x10102df
        0x10102e0
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    .line 151
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
