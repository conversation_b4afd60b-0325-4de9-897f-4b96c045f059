.class public abstract Landroidx/versionedparcelable/CustomVersionedParcelable;
.super Ljava/lang/Object;
.source "CustomVersionedParcelable.java"

# interfaces
.implements Landroidx/versionedparcelable/VersionedParcelable;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 27
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public onPostParceling()V
    .locals 0

    return-void
.end method

.method public onPreParceling(Z)V
    .locals 0

    return-void
.end method
