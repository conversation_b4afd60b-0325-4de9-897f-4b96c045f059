.class public final Landroidx/viewpager2/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager2/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static final accessibility_action_clickable_span:I = 0x7f0a0041

.field public static final accessibility_custom_action_0:I = 0x7f0a0042

.field public static final accessibility_custom_action_1:I = 0x7f0a0043

.field public static final accessibility_custom_action_10:I = 0x7f0a0044

.field public static final accessibility_custom_action_11:I = 0x7f0a0045

.field public static final accessibility_custom_action_12:I = 0x7f0a0046

.field public static final accessibility_custom_action_13:I = 0x7f0a0047

.field public static final accessibility_custom_action_14:I = 0x7f0a0048

.field public static final accessibility_custom_action_15:I = 0x7f0a0049

.field public static final accessibility_custom_action_16:I = 0x7f0a004a

.field public static final accessibility_custom_action_17:I = 0x7f0a004b

.field public static final accessibility_custom_action_18:I = 0x7f0a004c

.field public static final accessibility_custom_action_19:I = 0x7f0a004d

.field public static final accessibility_custom_action_2:I = 0x7f0a004e

.field public static final accessibility_custom_action_20:I = 0x7f0a004f

.field public static final accessibility_custom_action_21:I = 0x7f0a0050

.field public static final accessibility_custom_action_22:I = 0x7f0a0051

.field public static final accessibility_custom_action_23:I = 0x7f0a0052

.field public static final accessibility_custom_action_24:I = 0x7f0a0053

.field public static final accessibility_custom_action_25:I = 0x7f0a0054

.field public static final accessibility_custom_action_26:I = 0x7f0a0055

.field public static final accessibility_custom_action_27:I = 0x7f0a0056

.field public static final accessibility_custom_action_28:I = 0x7f0a0057

.field public static final accessibility_custom_action_29:I = 0x7f0a0058

.field public static final accessibility_custom_action_3:I = 0x7f0a0059

.field public static final accessibility_custom_action_30:I = 0x7f0a005a

.field public static final accessibility_custom_action_31:I = 0x7f0a005b

.field public static final accessibility_custom_action_4:I = 0x7f0a005c

.field public static final accessibility_custom_action_5:I = 0x7f0a005d

.field public static final accessibility_custom_action_6:I = 0x7f0a005e

.field public static final accessibility_custom_action_7:I = 0x7f0a005f

.field public static final accessibility_custom_action_8:I = 0x7f0a0060

.field public static final accessibility_custom_action_9:I = 0x7f0a0061

.field public static final action_container:I = 0x7f0a0081

.field public static final action_divider:I = 0x7f0a0089

.field public static final action_image:I = 0x7f0a0096

.field public static final action_text:I = 0x7f0a00c1

.field public static final actions:I = 0x7f0a00cc

.field public static final async:I = 0x7f0a00e1

.field public static final blocking:I = 0x7f0a00eb

.field public static final chronometer:I = 0x7f0a0123

.field public static final dialog_button:I = 0x7f0a016a

.field public static final forever:I = 0x7f0a01b7

.field public static final icon:I = 0x7f0a01e1

.field public static final icon_group:I = 0x7f0a01e3

.field public static final info:I = 0x7f0a01ee

.field public static final italic:I = 0x7f0a01f3

.field public static final item_touch_helper_previous_elevation:I = 0x7f0a01f5

.field public static final line1:I = 0x7f0a021a

.field public static final line3:I = 0x7f0a021b

.field public static final normal:I = 0x7f0a02b5

.field public static final notification_background:I = 0x7f0a02b7

.field public static final notification_main_column:I = 0x7f0a02b8

.field public static final notification_main_column_container:I = 0x7f0a02b9

.field public static final right_icon:I = 0x7f0a02fe

.field public static final right_side:I = 0x7f0a02ff

.field public static final tag_accessibility_actions:I = 0x7f0a037e

.field public static final tag_accessibility_clickable_spans:I = 0x7f0a037f

.field public static final tag_accessibility_heading:I = 0x7f0a0380

.field public static final tag_accessibility_pane_title:I = 0x7f0a0381

.field public static final tag_screen_reader_focusable:I = 0x7f0a0385

.field public static final tag_transition_group:I = 0x7f0a0387

.field public static final tag_unhandled_key_event_manager:I = 0x7f0a0388

.field public static final tag_unhandled_key_listeners:I = 0x7f0a0389

.field public static final text:I = 0x7f0a0393

.field public static final text2:I = 0x7f0a0394

.field public static final time:I = 0x7f0a03c5

.field public static final title:I = 0x7f0a03c6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
