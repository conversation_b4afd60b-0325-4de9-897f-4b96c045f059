.class public final Landroidx/viewpager/R$drawable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "drawable"
.end annotation


# static fields
.field public static final notification_action_background:I = 0x7f0801e4

.field public static final notification_bg:I = 0x7f0801e5

.field public static final notification_bg_low:I = 0x7f0801e6

.field public static final notification_bg_low_normal:I = 0x7f0801e7

.field public static final notification_bg_low_pressed:I = 0x7f0801e8

.field public static final notification_bg_normal:I = 0x7f0801e9

.field public static final notification_bg_normal_pressed:I = 0x7f0801ea

.field public static final notification_icon_background:I = 0x7f0801eb

.field public static final notification_template_icon_bg:I = 0x7f0801ed

.field public static final notification_template_icon_low_bg:I = 0x7f0801ee

.field public static final notification_tile_bg:I = 0x7f0801ef

.field public static final notify_panel_notification_icon_bg:I = 0x7f0801f0


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
