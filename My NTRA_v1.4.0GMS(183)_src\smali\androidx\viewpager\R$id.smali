.class public final Landroidx/viewpager/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static final action_container:I = 0x7f0a0081

.field public static final action_divider:I = 0x7f0a0089

.field public static final action_image:I = 0x7f0a0096

.field public static final action_text:I = 0x7f0a00c1

.field public static final actions:I = 0x7f0a00cc

.field public static final async:I = 0x7f0a00e1

.field public static final blocking:I = 0x7f0a00eb

.field public static final chronometer:I = 0x7f0a0123

.field public static final forever:I = 0x7f0a01b7

.field public static final icon:I = 0x7f0a01e1

.field public static final icon_group:I = 0x7f0a01e3

.field public static final info:I = 0x7f0a01ee

.field public static final italic:I = 0x7f0a01f3

.field public static final line1:I = 0x7f0a021a

.field public static final line3:I = 0x7f0a021b

.field public static final normal:I = 0x7f0a02b5

.field public static final notification_background:I = 0x7f0a02b7

.field public static final notification_main_column:I = 0x7f0a02b8

.field public static final notification_main_column_container:I = 0x7f0a02b9

.field public static final right_icon:I = 0x7f0a02fe

.field public static final right_side:I = 0x7f0a02ff

.field public static final tag_transition_group:I = 0x7f0a0387

.field public static final tag_unhandled_key_event_manager:I = 0x7f0a0388

.field public static final tag_unhandled_key_listeners:I = 0x7f0a0389

.field public static final text:I = 0x7f0a0393

.field public static final text2:I = 0x7f0a0394

.field public static final time:I = 0x7f0a03c5

.field public static final title:I = 0x7f0a03c6


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
