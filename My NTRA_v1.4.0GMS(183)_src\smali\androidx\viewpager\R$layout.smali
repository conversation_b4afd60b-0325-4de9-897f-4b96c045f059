.class public final Landroidx/viewpager/R$layout;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "layout"
.end annotation


# static fields
.field public static final notification_action:I = 0x7f0d00de

.field public static final notification_action_tombstone:I = 0x7f0d00df

.field public static final notification_template_custom_big:I = 0x7f0d00e6

.field public static final notification_template_icon_group:I = 0x7f0d00e7

.field public static final notification_template_part_chronometer:I = 0x7f0d00eb

.field public static final notification_template_part_time:I = 0x7f0d00ec


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
