.class public final Landroidx/viewpager/R$style;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "style"
.end annotation


# static fields
.field public static final TextAppearance_Compat_Notification:I = 0x7f1401f7

.field public static final TextAppearance_Compat_Notification_Info:I = 0x7f1401f8

.field public static final TextAppearance_Compat_Notification_Line2:I = 0x7f1401fa

.field public static final TextAppearance_Compat_Notification_Time:I = 0x7f1401fd

.field public static final TextAppearance_Compat_Notification_Title:I = 0x7f1401ff

.field public static final Widget_Compat_NotificationActionContainer:I = 0x7f140346

.field public static final Widget_Compat_NotificationActionText:I = 0x7f140347


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
