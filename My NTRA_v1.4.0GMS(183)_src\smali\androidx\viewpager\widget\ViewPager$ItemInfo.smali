.class Landroidx/viewpager/widget/ViewPager$ItemInfo;
.super Ljava/lang/Object;
.source "ViewPager.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/viewpager/widget/ViewPager;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x8
    name = "ItemInfo"
.end annotation


# instance fields
.field object:Ljava/lang/Object;

.field offset:F

.field position:I

.field scrolling:Z

.field widthFactor:F


# direct methods
.method constructor <init>()V
    .locals 0

    .line 132
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
