.class public final Landroidx/window/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/window/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static final activityAction:I = 0x7f04002a

.field public static final activityName:I = 0x7f04002c

.field public static final alwaysExpand:I = 0x7f040039

.field public static final clearTop:I = 0x7f0400cd

.field public static final finishPrimaryWithSecondary:I = 0x7f0401be

.field public static final finishSecondaryWithPrimary:I = 0x7f0401bf

.field public static final placeholderActivityName:I = 0x7f040345

.field public static final primaryActivityName:I = 0x7f040363

.field public static final secondaryActivityAction:I = 0x7f040390

.field public static final secondaryActivityName:I = 0x7f040391

.field public static final splitLayoutDirection:I = 0x7f0403b9

.field public static final splitMinSmallestWidth:I = 0x7f0403ba

.field public static final splitMinWidth:I = 0x7f0403bb

.field public static final splitRatio:I = 0x7f0403bc


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
