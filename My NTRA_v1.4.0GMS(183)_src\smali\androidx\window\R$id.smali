.class public final Landroidx/window/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/window/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static final androidx_window_activity_scope:I = 0x7f0a00d6

.field public static final locale:I = 0x7f0a0251

.field public static final ltr:I = 0x7f0a0254

.field public static final rtl:I = 0x7f0a0304


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
