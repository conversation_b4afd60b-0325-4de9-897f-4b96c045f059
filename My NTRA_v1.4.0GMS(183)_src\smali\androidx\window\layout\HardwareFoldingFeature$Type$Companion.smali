.class public final Landroidx/window/layout/HardwareFoldingFeature$Type$Companion;
.super Ljava/lang/Object;
.source "HardwareFoldingFeature.kt"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroidx/window/layout/HardwareFoldingFeature$Type;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Companion"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0005\u0008\u0080\u0003\u0018\u00002\u00020\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\u0008\n\u0000\u001a\u0004\u0008\u0008\u0010\u0006\u00a8\u0006\t"
    }
    d2 = {
        "Landroidx/window/layout/HardwareFoldingFeature$Type$Companion;",
        "",
        "()V",
        "FOLD",
        "Landroidx/window/layout/HardwareFoldingFeature$Type;",
        "getFOLD",
        "()Landroidx/window/layout/HardwareFoldingFeature$Type;",
        "HINGE",
        "getHINGE",
        "window_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x6,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 113
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    invoke-direct {p0}, Landroidx/window/layout/HardwareFoldingFeature$Type$Companion;-><init>()V

    return-void
.end method


# virtual methods
.method public final getFOLD()Landroidx/window/layout/HardwareFoldingFeature$Type;
    .locals 1

    .line 117
    invoke-static {}, Landroidx/window/layout/HardwareFoldingFeature$Type;->access$getFOLD$cp()Landroidx/window/layout/HardwareFoldingFeature$Type;

    move-result-object v0

    return-object v0
.end method

.method public final getHINGE()Landroidx/window/layout/HardwareFoldingFeature$Type;
    .locals 1

    .line 122
    invoke-static {}, Landroidx/window/layout/HardwareFoldingFeature$Type;->access$getHINGE$cp()Landroidx/window/layout/HardwareFoldingFeature$Type;

    move-result-object v0

    return-object v0
.end method
