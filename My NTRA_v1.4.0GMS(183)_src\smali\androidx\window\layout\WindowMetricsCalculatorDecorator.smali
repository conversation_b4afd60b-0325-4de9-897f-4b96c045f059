.class public interface abstract Landroidx/window/layout/WindowMetricsCalculatorDecorator;
.super Ljava/lang/Object;
.source "WindowMetricsCalculator.kt"


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0008g\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0003H\'\u00a8\u0006\u0005"
    }
    d2 = {
        "Landroidx/window/layout/WindowMetricsCalculatorDecorator;",
        "",
        "decorate",
        "Landroidx/window/layout/WindowMetricsCalculator;",
        "calculator",
        "window_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x6,
        0x0
    }
    xi = 0x30
.end annotation


# virtual methods
.method public abstract decorate(Landroidx/window/layout/WindowMetricsCalculator;)Landroidx/window/layout/WindowMetricsCalculator;
.end method
