.class interface abstract Lcom/bumptech/glide/load/engine/cache/MemorySizeCalculator$ScreenDimensions;
.super Ljava/lang/Object;
.source "MemorySizeCalculator.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/bumptech/glide/load/engine/cache/MemorySizeCalculator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "ScreenDimensions"
.end annotation


# virtual methods
.method public abstract getHeightPixels()I
.end method

.method public abstract getWidthPixels()I
.end method
