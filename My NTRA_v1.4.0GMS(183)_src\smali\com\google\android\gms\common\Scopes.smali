.class public final Lcom/google/android/gms/common/Scopes;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-basement@@18.1.0"


# static fields
.field public static final APP_STATE:Ljava/lang/String; = "https://www.googleapis.com/auth/appstate"

.field public static final CLOUD_SAVE:Ljava/lang/String; = "https://www.googleapis.com/auth/datastoremobile"

.field public static final DRIVE_APPFOLDER:Ljava/lang/String; = "https://www.googleapis.com/auth/drive.appdata"

.field public static final DRIVE_APPS:Ljava/lang/String; = "https://www.googleapis.com/auth/drive.apps"

.field public static final DRIVE_FILE:Ljava/lang/String; = "https://www.googleapis.com/auth/drive.file"

.field public static final DRIVE_FULL:Ljava/lang/String; = "https://www.googleapis.com/auth/drive"

.field public static final EMAIL:Ljava/lang/String; = "email"

.field public static final GAMES:Ljava/lang/String; = "https://www.googleapis.com/auth/games"

.field public static final GAMES_LITE:Ljava/lang/String; = "https://www.googleapis.com/auth/games_lite"

.field public static final LEGACY_USERINFO_EMAIL:Ljava/lang/String; = "https://www.googleapis.com/auth/userinfo.email"

.field public static final LEGACY_USERINFO_PROFILE:Ljava/lang/String; = "https://www.googleapis.com/auth/userinfo.profile"

.field public static final OPEN_ID:Ljava/lang/String; = "openid"

.field public static final PLUS_LOGIN:Ljava/lang/String; = "https://www.googleapis.com/auth/plus.login"
    .annotation runtime Ljava/lang/Deprecated;
    .end annotation
.end field

.field public static final PLUS_ME:Ljava/lang/String; = "https://www.googleapis.com/auth/plus.me"

.field public static final PROFILE:Ljava/lang/String; = "profile"


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
