.class public interface abstract Lcom/google/android/gms/common/api/Api$ApiOptions$HasGoogleSignInAccountOptions;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-base@@18.0.1"

# interfaces
.implements Lcom/google/android/gms/common/api/Api$ApiOptions$HasOptions;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/gms/common/api/Api$ApiOptions;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "HasGoogleSignInAccountOptions"
.end annotation


# virtual methods
.method public abstract getGoogleSignInAccount()Lcom/google/android/gms/auth/api/signin/GoogleSignInAccount;
.end method
