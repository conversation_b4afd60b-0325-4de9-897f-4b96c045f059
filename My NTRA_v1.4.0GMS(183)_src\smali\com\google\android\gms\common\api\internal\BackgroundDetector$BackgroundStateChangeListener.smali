.class public interface abstract Lcom/google/android/gms/common/api/internal/BackgroundDetector$BackgroundStateChangeListener;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-basement@@18.1.0"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/gms/common/api/internal/BackgroundDetector;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "BackgroundStateChangeListener"
.end annotation


# virtual methods
.method public abstract onBackgroundStateChanged(Z)V
.end method
