.class public final Lcom/google/android/gms/common/internal/Constants;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-basement@@18.1.0"


# static fields
.field public static final ACTION_LOAD_IMAGE:Ljava/lang/String; = "com.google.android.gms.common.images.LOAD_IMAGE"

.field public static final EXTRA_PRIORITY:Ljava/lang/String; = "com.google.android.gms.extras.priority"

.field public static final EXTRA_RESULT_RECEIVER:Ljava/lang/String; = "com.google.android.gms.extras.resultReceiver"

.field public static final EXTRA_URI:Ljava/lang/String; = "com.google.android.gms.extras.uri"

.field public static final KEY_GMS_ERROR_CODE:Ljava/lang/String; = "gms_error_code"

.field public static final KEY_NETWORK_TO_USE:Ljava/lang/String; = "networkToUse"


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
