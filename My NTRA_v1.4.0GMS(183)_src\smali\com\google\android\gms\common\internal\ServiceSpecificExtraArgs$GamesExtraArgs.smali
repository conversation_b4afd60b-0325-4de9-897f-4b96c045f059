.class public interface abstract Lcom/google/android/gms/common/internal/ServiceSpecificExtraArgs$GamesExtraArgs;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-basement@@18.1.0"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/gms/common/internal/ServiceSpecificExtraArgs;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "GamesExtraArgs"
.end annotation


# static fields
.field public static final DESIRED_LOCALE:Ljava/lang/String; = "com.google.android.gms.games.key.desiredLocale"

.field public static final GAME_PACKAGE_NAME:Ljava/lang/String; = "com.google.android.gms.games.key.gamePackageName"

.field public static final SIGNIN_OPTIONS:Ljava/lang/String; = "com.google.android.gms.games.key.signInOptions"

.field public static final WINDOW_TOKEN:Ljava/lang/String; = "com.google.android.gms.games.key.popupWindowToken"
