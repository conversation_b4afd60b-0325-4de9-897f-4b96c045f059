.class public interface abstract Lcom/google/android/gms/common/stats/StatsEvent$Types;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-basement@@18.1.0"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/android/gms/common/stats/StatsEvent;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Types"
.end annotation


# static fields
.field public static final EVENT_TYPE_ACQUIRE_WAKE_LOCK:I = 0x7

.field public static final EVENT_TYPE_RELEASE_WAKE_LOCK:I = 0x8
