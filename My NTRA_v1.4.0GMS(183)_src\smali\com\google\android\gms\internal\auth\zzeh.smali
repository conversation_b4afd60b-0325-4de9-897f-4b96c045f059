.class abstract Lcom/google/android/gms/internal/auth/zzeh;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-auth-base@@17.1.4"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "Lcom/google/android/gms/internal/auth/zzek<",
        "TT;>;>",
        "Ljava/lang/Object;"
    }
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method abstract zza(Ljava/lang/Object;)Lcom/google/android/gms/internal/auth/zzel;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            ")",
            "Lcom/google/android/gms/internal/auth/zzel<",
            "TT;>;"
        }
    .end annotation
.end method

.method abstract zzb(Ljava/lang/Object;)V
.end method

.method abstract zzc(Lcom/google/android/gms/internal/auth/zzfq;)Z
.end method
