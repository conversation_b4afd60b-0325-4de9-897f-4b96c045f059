.class public interface abstract Lcom/google/android/gms/internal/auth/zzek;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-auth-base@@17.1.4"

# interfaces
.implements Ljava/lang/Comparable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T::",
        "Lcom/google/android/gms/internal/auth/zzek<",
        "TT;>;>",
        "Ljava/lang/Object;",
        "Ljava/lang/Comparable<",
        "TT;>;"
    }
.end annotation


# virtual methods
.method public abstract zza()I
.end method

.method public abstract zzb()Lcom/google/android/gms/internal/auth/zzhe;
.end method

.method public abstract zzc()Z
.end method
