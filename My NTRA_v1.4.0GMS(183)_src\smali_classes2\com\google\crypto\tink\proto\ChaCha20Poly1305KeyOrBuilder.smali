.class public interface abstract Lcom/google/crypto/tink/proto/ChaCha20Poly1305KeyOrBuilder;
.super Ljava/lang/Object;
.source "ChaCha20Poly1305KeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getVersion()I
.end method
