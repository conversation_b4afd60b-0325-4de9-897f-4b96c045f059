.class public interface abstract Lcom/google/crypto/tink/proto/EcdsaParamsOrBuilder;
.super Ljava/lang/Object;
.source "EcdsaParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getCurve()Lcom/google/crypto/tink/proto/EllipticCurveType;
.end method

.method public abstract getCurveValue()I
.end method

.method public abstract getEncoding()Lcom/google/crypto/tink/proto/EcdsaSignatureEncoding;
.end method

.method public abstract getEncodingValue()I
.end method

.method public abstract getHashType()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getHashTypeValue()I
.end method
