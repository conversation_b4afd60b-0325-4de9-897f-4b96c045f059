.class public interface abstract Lcom/google/crypto/tink/proto/EcdsaPrivateKeyOrBuilder;
.super Ljava/lang/Object;
.source "EcdsaPrivateKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getPublicKey()Lcom/google/crypto/tink/proto/EcdsaPublicKey;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasPublicKey()Z
.end method
