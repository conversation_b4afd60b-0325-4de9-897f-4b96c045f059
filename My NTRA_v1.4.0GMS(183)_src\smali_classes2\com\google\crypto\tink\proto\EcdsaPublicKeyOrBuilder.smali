.class public interface abstract Lcom/google/crypto/tink/proto/EcdsaPublicKeyOrBuilder;
.super Ljava/lang/Object;
.source "EcdsaPublicKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getParams()Lcom/google/crypto/tink/proto/EcdsaParams;
.end method

.method public abstract getVersion()I
.end method

.method public abstract getX()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getY()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract hasParams()Z
.end method
