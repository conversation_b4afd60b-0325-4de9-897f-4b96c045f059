.class public interface abstract Lcom/google/crypto/tink/proto/EciesAeadDemParamsOrBuilder;
.super Ljava/lang/Object;
.source "EciesAeadDemParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getAeadDem()Lcom/google/crypto/tink/proto/KeyTemplate;
.end method

.method public abstract hasAeadDem()Z
.end method
