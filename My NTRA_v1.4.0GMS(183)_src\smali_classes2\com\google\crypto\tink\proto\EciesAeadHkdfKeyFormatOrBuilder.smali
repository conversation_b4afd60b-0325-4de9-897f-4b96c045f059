.class public interface abstract Lcom/google/crypto/tink/proto/EciesAeadHkdfKeyFormatOrBuilder;
.super Ljava/lang/Object;
.source "EciesAeadHkdfKeyFormatOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getParams()Lcom/google/crypto/tink/proto/EciesAeadHkdfParams;
.end method

.method public abstract hasParams()Z
.end method
