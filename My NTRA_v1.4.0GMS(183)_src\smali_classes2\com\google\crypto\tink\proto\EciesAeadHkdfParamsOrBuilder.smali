.class public interface abstract Lcom/google/crypto/tink/proto/EciesAeadHkdfParamsOrBuilder;
.super Ljava/lang/Object;
.source "EciesAeadHkdfParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getDemParams()Lcom/google/crypto/tink/proto/EciesAeadDemParams;
.end method

.method public abstract getEcPointFormat()Lcom/google/crypto/tink/proto/EcPointFormat;
.end method

.method public abstract getEcPointFormatValue()I
.end method

.method public abstract getKemParams()Lcom/google/crypto/tink/proto/EciesHkdfKemParams;
.end method

.method public abstract hasDemParams()Z
.end method

.method public abstract hasKemParams()Z
.end method
