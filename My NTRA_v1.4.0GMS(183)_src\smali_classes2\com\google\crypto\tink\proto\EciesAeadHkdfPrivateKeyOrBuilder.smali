.class public interface abstract Lcom/google/crypto/tink/proto/EciesAeadHkdfPrivateKeyOrBuilder;
.super Ljava/lang/Object;
.source "EciesAeadHkdfPrivateKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getPublicKey()Lcom/google/crypto/tink/proto/EciesAeadHkdfPublicKey;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasPublicKey()Z
.end method
