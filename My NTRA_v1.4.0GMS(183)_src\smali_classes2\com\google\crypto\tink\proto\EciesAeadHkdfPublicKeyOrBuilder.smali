.class public interface abstract Lcom/google/crypto/tink/proto/EciesAeadHkdfPublicKeyOrBuilder;
.super Ljava/lang/Object;
.source "EciesAeadHkdfPublicKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getParams()Lcom/google/crypto/tink/proto/EciesAeadHkdfParams;
.end method

.method public abstract getVersion()I
.end method

.method public abstract getX()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getY()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract hasParams()Z
.end method
