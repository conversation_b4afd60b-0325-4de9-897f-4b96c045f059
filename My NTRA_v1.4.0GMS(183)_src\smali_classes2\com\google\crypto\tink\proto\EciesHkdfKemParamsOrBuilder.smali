.class public interface abstract Lcom/google/crypto/tink/proto/EciesHkdfKemParamsOrBuilder;
.super Ljava/lang/Object;
.source "EciesHkdfKemParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getCurveType()Lcom/google/crypto/tink/proto/EllipticCurveType;
.end method

.method public abstract getCurveTypeValue()I
.end method

.method public abstract getHkdfHashType()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getHkdfHashTypeValue()I
.end method

.method public abstract getHkdfSalt()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method
