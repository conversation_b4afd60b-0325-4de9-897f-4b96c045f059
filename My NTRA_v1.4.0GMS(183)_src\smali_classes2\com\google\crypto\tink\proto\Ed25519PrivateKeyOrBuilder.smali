.class public interface abstract Lcom/google/crypto/tink/proto/Ed25519PrivateKeyOrBuilder;
.super Ljava/lang/Object;
.source "Ed25519PrivateKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getPublicKey()Lcom/google/crypto/tink/proto/Ed25519PublicKey;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasPublicKey()Z
.end method
