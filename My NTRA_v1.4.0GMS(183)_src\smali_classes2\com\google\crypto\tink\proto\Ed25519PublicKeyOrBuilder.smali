.class public interface abstract Lcom/google/crypto/tink/proto/Ed25519PublicKeyOrBuilder;
.super Ljava/lang/Object;
.source "Ed25519PublicKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getVersion()I
.end method
