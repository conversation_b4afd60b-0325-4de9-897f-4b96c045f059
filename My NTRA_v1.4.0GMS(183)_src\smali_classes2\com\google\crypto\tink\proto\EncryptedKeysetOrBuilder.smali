.class public interface abstract Lcom/google/crypto/tink/proto/EncryptedKeysetOrBuilder;
.super Ljava/lang/Object;
.source "EncryptedKeysetOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getEncryptedKeyset()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getKeysetInfo()Lcom/google/crypto/tink/proto/KeysetInfo;
.end method

.method public abstract hasKeysetInfo()Z
.end method
