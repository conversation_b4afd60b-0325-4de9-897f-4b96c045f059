.class public interface abstract Lcom/google/crypto/tink/proto/HkdfPrfKeyOrBuilder;
.super Ljava/lang/Object;
.source "HkdfPrfKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getParams()Lcom/google/crypto/tink/proto/HkdfPrfParams;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasParams()Z
.end method
