.class public interface abstract Lcom/google/crypto/tink/proto/HkdfPrfParamsOrBuilder;
.super Ljava/lang/Object;
.source "HkdfPrfParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getHash()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getHashValue()I
.end method

.method public abstract getSalt()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method
