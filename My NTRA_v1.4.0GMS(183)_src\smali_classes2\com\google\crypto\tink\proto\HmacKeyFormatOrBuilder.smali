.class public interface abstract Lcom/google/crypto/tink/proto/HmacKeyFormatOrBuilder;
.super Ljava/lang/Object;
.source "HmacKeyFormatOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeySize()I
.end method

.method public abstract getParams()Lcom/google/crypto/tink/proto/HmacParams;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasParams()Z
.end method
