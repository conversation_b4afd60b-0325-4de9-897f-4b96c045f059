.class public interface abstract Lcom/google/crypto/tink/proto/HmacParamsOrBuilder;
.super Ljava/lang/Object;
.source "HmacParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getHash()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getHashValue()I
.end method

.method public abstract getTagSize()I
.end method
