.class public interface abstract Lcom/google/crypto/tink/proto/HmacPrfKeyFormatOrBuilder;
.super Ljava/lang/Object;
.source "HmacPrfKeyFormatOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeySize()I
.end method

.method public abstract getParams()Lcom/google/crypto/tink/proto/HmacPrfParams;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasParams()Z
.end method
