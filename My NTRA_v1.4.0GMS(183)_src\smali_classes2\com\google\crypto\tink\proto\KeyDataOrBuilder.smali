.class public interface abstract Lcom/google/crypto/tink/proto/KeyDataOrBuilder;
.super Ljava/lang/Object;
.source "KeyDataOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getKeyMaterialType()Lcom/google/crypto/tink/proto/KeyData$KeyMaterialType;
.end method

.method public abstract getKeyMaterialTypeValue()I
.end method

.method public abstract getTypeUrl()Ljava/lang/String;
.end method

.method public abstract getTypeUrlBytes()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method
