.class public interface abstract Lcom/google/crypto/tink/proto/KeyTemplateOrBuilder;
.super Ljava/lang/Object;
.source "KeyTemplateOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getOutputPrefixType()Lcom/google/crypto/tink/proto/OutputPrefixType;
.end method

.method public abstract getOutputPrefixTypeValue()I
.end method

.method public abstract getTypeUrl()Ljava/lang/String;
.end method

.method public abstract getTypeUrlBytes()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getValue()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method
