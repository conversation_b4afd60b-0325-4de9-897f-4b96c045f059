.class public interface abstract Lcom/google/crypto/tink/proto/KeyTypeEntryOrBuilder;
.super Ljava/lang/Object;
.source "KeyTypeEntryOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# annotations
.annotation runtime Ljava/lang/Deprecated;
.end annotation


# virtual methods
.method public abstract getCatalogueName()Ljava/lang/String;
.end method

.method public abstract getCatalogueNameBytes()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getKeyManagerVersion()I
.end method

.method public abstract getNewKeyAllowed()Z
.end method

.method public abstract getPrimitiveName()Ljava/lang/String;
.end method

.method public abstract getPrimitiveNameBytes()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getTypeUrl()Ljava/lang/String;
.end method

.method public abstract getTypeUrlBytes()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method
