.class public interface abstract Lcom/google/crypto/tink/proto/KmsAeadKeyOrBuilder;
.super Ljava/lang/Object;
.source "KmsAeadKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getParams()Lcom/google/crypto/tink/proto/KmsAeadKeyFormat;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasParams()Z
.end method
