.class public interface abstract Lcom/google/crypto/tink/proto/KmsEnvelopeAeadKeyFormatOrBuilder;
.super Ljava/lang/Object;
.source "KmsEnvelopeAeadKeyFormatOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getDekTemplate()Lcom/google/crypto/tink/proto/KeyTemplate;
.end method

.method public abstract getKekUri()Ljava/lang/String;
.end method

.method public abstract getKekUriBytes()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract hasDekTemplate()Z
.end method
