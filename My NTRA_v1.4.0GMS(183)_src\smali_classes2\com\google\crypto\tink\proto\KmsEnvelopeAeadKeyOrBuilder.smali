.class public interface abstract Lcom/google/crypto/tink/proto/KmsEnvelopeAeadKeyOrBuilder;
.super Ljava/lang/Object;
.source "KmsEnvelopeAeadKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getParams()Lcom/google/crypto/tink/proto/KmsEnvelopeAeadKeyFormat;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasParams()Z
.end method
