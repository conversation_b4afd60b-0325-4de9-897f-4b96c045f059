.class public interface abstract Lcom/google/crypto/tink/proto/RsaSsaPkcs1KeyFormatOrBuilder;
.super Ljava/lang/Object;
.source "RsaSsaPkcs1KeyFormatOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getModulusSizeInBits()I
.end method

.method public abstract getParams()Lcom/google/crypto/tink/proto/RsaSsaPkcs1Params;
.end method

.method public abstract getPublicExponent()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract hasParams()Z
.end method
