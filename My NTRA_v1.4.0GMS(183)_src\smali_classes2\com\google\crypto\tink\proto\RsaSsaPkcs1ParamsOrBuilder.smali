.class public interface abstract Lcom/google/crypto/tink/proto/RsaSsaPkcs1ParamsOrBuilder;
.super Ljava/lang/Object;
.source "RsaSsaPkcs1ParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getHashType()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getHashTypeValue()I
.end method
