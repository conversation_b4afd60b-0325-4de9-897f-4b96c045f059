.class public interface abstract Lcom/google/crypto/tink/proto/RsaSsaPkcs1PrivateKeyOrBuilder;
.super Ljava/lang/Object;
.source "RsaSsaPkcs1PrivateKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getCrt()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getD()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getDp()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getDq()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getP()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getPublicKey()Lcom/google/crypto/tink/proto/RsaSsaPkcs1PublicKey;
.end method

.method public abstract getQ()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasPublicKey()Z
.end method
