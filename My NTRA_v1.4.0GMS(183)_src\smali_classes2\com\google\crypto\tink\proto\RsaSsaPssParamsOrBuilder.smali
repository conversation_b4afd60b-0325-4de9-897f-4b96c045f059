.class public interface abstract Lcom/google/crypto/tink/proto/RsaSsaPssParamsOrBuilder;
.super Ljava/lang/Object;
.source "RsaSsaPssParamsOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getMgf1Hash()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getMgf1HashValue()I
.end method

.method public abstract getSaltLength()I
.end method

.method public abstract getSigHash()Lcom/google/crypto/tink/proto/HashType;
.end method

.method public abstract getSigHashValue()I
.end method
