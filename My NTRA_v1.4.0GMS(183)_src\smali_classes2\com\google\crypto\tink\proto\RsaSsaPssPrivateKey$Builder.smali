.class public final Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
.super Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite$Builder;
.source "RsaSsaPssPrivateKey.java"

# interfaces
.implements Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKeyOrBuilder;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "Builder"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite$Builder<",
        "Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;",
        "Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;",
        ">;",
        "Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKeyOrBuilder;"
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 1

    .line 498
    invoke-static {}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$000()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite$Builder;-><init>(Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;)V

    return-void
.end method

.method synthetic constructor <init>(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$1;)V
    .locals 0

    .line 491
    invoke-direct {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;-><init>()V

    return-void
.end method


# virtual methods
.method public clearCrt()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 887
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 888
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1700(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearD()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 654
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 655
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$700(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearDp()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 795
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 796
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1300(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearDq()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 841
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 842
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1500(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearP()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 703
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 704
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$900(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearPublicKey()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 608
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 609
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$500(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearQ()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 749
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 750
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1100(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public clearVersion()Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1

    .line 537
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 538
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$200(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;)V

    return-object p0
.end method

.method public getCrt()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
    .locals 1

    .line 858
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getCrt()Lcom/google/crypto/tink/shaded/protobuf/ByteString;

    move-result-object v0

    return-object v0
.end method

.method public getD()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
    .locals 1

    .line 625
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getD()Lcom/google/crypto/tink/shaded/protobuf/ByteString;

    move-result-object v0

    return-object v0
.end method

.method public getDp()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
    .locals 1

    .line 766
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getDp()Lcom/google/crypto/tink/shaded/protobuf/ByteString;

    move-result-object v0

    return-object v0
.end method

.method public getDq()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
    .locals 1

    .line 812
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getDq()Lcom/google/crypto/tink/shaded/protobuf/ByteString;

    move-result-object v0

    return-object v0
.end method

.method public getP()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
    .locals 1

    .line 672
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getP()Lcom/google/crypto/tink/shaded/protobuf/ByteString;

    move-result-object v0

    return-object v0
.end method

.method public getPublicKey()Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;
    .locals 1

    .line 562
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getPublicKey()Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;

    move-result-object v0

    return-object v0
.end method

.method public getQ()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
    .locals 1

    .line 720
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getQ()Lcom/google/crypto/tink/shaded/protobuf/ByteString;

    move-result-object v0

    return-object v0
.end method

.method public getVersion()I
    .locals 1

    .line 512
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->getVersion()I

    move-result v0

    return v0
.end method

.method public hasPublicKey()Z
    .locals 1

    .line 551
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {v0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->hasPublicKey()Z

    move-result v0

    return v0
.end method

.method public mergePublicKey(Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 597
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 598
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$400(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;)V

    return-object p0
.end method

.method public setCrt(Lcom/google/crypto/tink/shaded/protobuf/ByteString;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 872
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 873
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1600(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/shaded/protobuf/ByteString;)V

    return-object p0
.end method

.method public setD(Lcom/google/crypto/tink/shaded/protobuf/ByteString;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 639
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 640
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$600(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/shaded/protobuf/ByteString;)V

    return-object p0
.end method

.method public setDp(Lcom/google/crypto/tink/shaded/protobuf/ByteString;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 780
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 781
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1200(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/shaded/protobuf/ByteString;)V

    return-object p0
.end method

.method public setDq(Lcom/google/crypto/tink/shaded/protobuf/ByteString;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 826
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 827
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1400(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/shaded/protobuf/ByteString;)V

    return-object p0
.end method

.method public setP(Lcom/google/crypto/tink/shaded/protobuf/ByteString;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 687
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 688
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$800(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/shaded/protobuf/ByteString;)V

    return-object p0
.end method

.method public setPublicKey(Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey$Builder;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "builderForValue"
        }
    .end annotation

    .line 585
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 586
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-virtual {p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey$Builder;->build()Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    move-result-object p1

    check-cast p1, Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$300(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;)V

    return-object p0
.end method

.method public setPublicKey(Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 572
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 573
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$300(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/proto/RsaSsaPssPublicKey;)V

    return-object p0
.end method

.method public setQ(Lcom/google/crypto/tink/shaded/protobuf/ByteString;)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 734
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 735
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$1000(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;Lcom/google/crypto/tink/shaded/protobuf/ByteString;)V

    return-object p0
.end method

.method public setVersion(I)Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;
    .locals 1
    .annotation system Ldalvik/annotation/MethodParameters;
        accessFlags = {
            0x0
        }
        names = {
            "value"
        }
    .end annotation

    .line 524
    invoke-virtual {p0}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->copyOnWrite()V

    .line 525
    iget-object v0, p0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey$Builder;->instance:Lcom/google/crypto/tink/shaded/protobuf/GeneratedMessageLite;

    check-cast v0, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;

    invoke-static {v0, p1}, Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;->access$100(Lcom/google/crypto/tink/proto/RsaSsaPssPrivateKey;I)V

    return-object p0
.end method
