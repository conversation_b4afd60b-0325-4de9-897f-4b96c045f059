.class public interface abstract Lcom/google/crypto/tink/proto/RsaSsaPssPublicKeyOrBuilder;
.super Ljava/lang/Object;
.source "RsaSsaPssPublicKeyOrBuilder.java"

# interfaces
.implements Lcom/google/crypto/tink/shaded/protobuf/MessageLiteOrBuilder;


# virtual methods
.method public abstract getE()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getN()Lcom/google/crypto/tink/shaded/protobuf/ByteString;
.end method

.method public abstract getParams()Lcom/google/crypto/tink/proto/RsaSsaPssParams;
.end method

.method public abstract getVersion()I
.end method

.method public abstract hasParams()Z
.end method
