.class public final Lkotlinx/parcelize/Parceler$DefaultImpls;
.super Ljava/lang/Object;
.source "Parceler.kt"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lkotlinx/parcelize/Parceler;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "DefaultImpls"
.end annotation

.annotation runtime Lkotlin/Metadata;
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# direct methods
.method public static newArray(Lkotlinx/parcelize/Parceler;I)[Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Lkotlinx/parcelize/Parceler<",
            "TT;>;I)[TT;"
        }
    .end annotation

    .line 40
    new-instance p0, <PERSON><PERSON><PERSON>/NotImplementedError;

    const-string p1, "Generated by Android Extensions automatically"

    invoke-direct {p0, p1}, <PERSON><PERSON><PERSON>/NotImplementedError;-><init>(Ljava/lang/String;)V

    throw p0
.end method
