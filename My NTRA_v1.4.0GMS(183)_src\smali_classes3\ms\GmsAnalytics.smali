.class public final Lms/GmsAnalytics;
.super Ljava/lang/Object;
.source "GmsAnalytics.kt"

# interfaces
.implements Leg/gov/tra/util/mobile_services/MsAnalytics;


# annotations
.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nGmsAnalytics.kt\nKotlin\n*S Kotlin\n*F\n+ 1 GmsAnalytics.kt\nms/GmsAnalytics\n+ 2 _Maps.kt\nkotlin/collections/MapsKt___MapsKt\n*L\n1#1,63:1\n215#2,2:64\n*S KotlinDebug\n*F\n+ 1 GmsAnalytics.kt\nms/GmsAnalytics\n*L\n28#1:64,2\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010$\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0007\u0012\u0008\u0008\u0002\u0010\u0008\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\"\u0010\u000b\u001a\u00020\u000c2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0008\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0016J\u001c\u0010\u0012\u001a\u00020\u000c2\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u000e0\u0014H\u0016J\u0018\u0010\u0015\u001a\u00020\u000c2\u0006\u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u0017\u001a\u00020\u000eH\u0016J\u0014\u0010\u0018\u001a\u00020\u0011*\u00020\u00112\u0006\u0010\u000f\u001a\u00020\u000eH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0008\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"
    }
    d2 = {
        "Lms/GmsAnalytics;",
        "Leg/gov/tra/util/mobile_services/MsAnalytics;",
        "firebaseAnalytics",
        "Lcom/google/firebase/analytics/FirebaseAnalytics;",
        "securePrefDataSource",
        "Leg/gov/tra/data/local/pref/SecurePrefDataSource;",
        "sessionManager",
        "Leg/gov/tra/util/SessionManager;",
        "gson",
        "Lcom/google/gson/Gson;",
        "(Lcom/google/firebase/analytics/FirebaseAnalytics;Leg/gov/tra/data/local/pref/SecurePrefDataSource;Leg/gov/tra/util/SessionManager;Lcom/google/gson/Gson;)V",
        "logEvents",
        "",
        "eventName",
        "",
        "status",
        "bundle",
        "Landroid/os/Bundle;",
        "setRemoteProperties",
        "properties",
        "",
        "setRemoteProperty",
        "key",
        "value",
        "appendInfo",
        "My_NTRA-1.4.0(183)_gmsRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final firebaseAnalytics:Lcom/google/firebase/analytics/FirebaseAnalytics;

.field private final gson:Lcom/google/gson/Gson;

.field private final securePrefDataSource:Leg/gov/tra/data/local/pref/SecurePrefDataSource;

.field private final sessionManager:Leg/gov/tra/util/SessionManager;


# direct methods
.method public constructor <init>(Lcom/google/firebase/analytics/FirebaseAnalytics;Leg/gov/tra/data/local/pref/SecurePrefDataSource;Leg/gov/tra/util/SessionManager;Lcom/google/gson/Gson;)V
    .locals 1

    const-string v0, "firebaseAnalytics"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "securePrefDataSource"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "sessionManager"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "gson"

    invoke-static {p4, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 15
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 16
    iput-object p1, p0, Lms/GmsAnalytics;->firebaseAnalytics:Lcom/google/firebase/analytics/FirebaseAnalytics;

    .line 17
    iput-object p2, p0, Lms/GmsAnalytics;->securePrefDataSource:Leg/gov/tra/data/local/pref/SecurePrefDataSource;

    .line 18
    iput-object p3, p0, Lms/GmsAnalytics;->sessionManager:Leg/gov/tra/util/SessionManager;

    .line 19
    iput-object p4, p0, Lms/GmsAnalytics;->gson:Lcom/google/gson/Gson;

    return-void
.end method

.method public synthetic constructor <init>(Lcom/google/firebase/analytics/FirebaseAnalytics;Leg/gov/tra/data/local/pref/SecurePrefDataSource;Leg/gov/tra/util/SessionManager;Lcom/google/gson/Gson;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p6, p5, 0x4

    if-eqz p6, :cond_0

    .line 18
    sget-object p3, Leg/gov/tra/util/SessionManager;->INSTANCE:Leg/gov/tra/util/SessionManager;

    :cond_0
    and-int/lit8 p5, p5, 0x8

    if-eqz p5, :cond_1

    .line 19
    new-instance p4, Lcom/google/gson/Gson;

    invoke-direct {p4}, Lcom/google/gson/Gson;-><init>()V

    .line 15
    :cond_1
    invoke-direct {p0, p1, p2, p3, p4}, Lms/GmsAnalytics;-><init>(Lcom/google/firebase/analytics/FirebaseAnalytics;Leg/gov/tra/data/local/pref/SecurePrefDataSource;Leg/gov/tra/util/SessionManager;Lcom/google/gson/Gson;)V

    return-void
.end method

.method private final appendInfo(Landroid/os/Bundle;Ljava/lang/String;)Landroid/os/Bundle;
    .locals 4

    const-string v0, "OperatorCode_dev"

    .line 41
    invoke-virtual {p1, v0}, Landroid/os/Bundle;->containsKey(Ljava/lang/String;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 42
    iget-object v1, p0, Lms/GmsAnalytics;->sessionManager:Leg/gov/tra/util/SessionManager;

    invoke-virtual {v1}, Leg/gov/tra/util/SessionManager;->getCurrentUser()Leg/gov/tra/util/SessionData;

    move-result-object v1

    if-eqz v1, :cond_0

    invoke-virtual {v1}, Leg/gov/tra/util/SessionData;->getUserType()Leg/gov/tra/util/UserType;

    move-result-object v1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    sget-object v2, Leg/gov/tra/util/UserType$Registered;->INSTANCE:Leg/gov/tra/util/UserType$Registered;

    invoke-static {v1, v2}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_1

    .line 43
    iget-object v1, p0, Lms/GmsAnalytics;->securePrefDataSource:Leg/gov/tra/data/local/pref/SecurePrefDataSource;

    invoke-interface {v1}, Leg/gov/tra/data/local/pref/SecurePrefDataSource;->getLoginData()Ljava/lang/String;

    move-result-object v1

    .line 44
    move-object v2, v1

    check-cast v2, Ljava/lang/CharSequence;

    invoke-static {v2}, Lkotlin/text/StringsKt;->isBlank(Ljava/lang/CharSequence;)Z

    move-result v2

    xor-int/lit8 v2, v2, 0x1

    if-eqz v2, :cond_2

    .line 45
    iget-object v2, p0, Lms/GmsAnalytics;->gson:Lcom/google/gson/Gson;

    const-class v3, Leg/gov/tra/data/model/UserData;

    invoke-virtual {v2, v1, v3}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Leg/gov/tra/data/model/UserData;

    .line 48
    invoke-virtual {v1}, Leg/gov/tra/data/model/UserData;->getNumber()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Leg/gov/tra/util/extensions/StringExtKt;->toOperatorCode(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    .line 46
    invoke-virtual {p1, v0, v1}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    :cond_1
    const-string v1, "Guest_dev"

    .line 52
    invoke-virtual {p1, v0, v1}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    :goto_1
    const-string v0, "MobileType_dev"

    .line 56
    sget-object v1, Landroid/os/Build;->MODEL:Ljava/lang/String;

    invoke-virtual {p1, v0, v1}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "Status_dev"

    .line 57
    invoke-virtual {p1, v0, p2}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    const-string p2, "OS_dev"

    const-string v0, "Android_dev"

    .line 58
    invoke-virtual {p1, p2, v0}, Landroid/os/Bundle;->putString(Ljava/lang/String;Ljava/lang/String;)V

    return-object p1
.end method


# virtual methods
.method public logEvents(Ljava/lang/String;Ljava/lang/String;Landroid/os/Bundle;)V
    .locals 1

    const-string v0, "eventName"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "status"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 23
    iget-object v0, p0, Lms/GmsAnalytics;->firebaseAnalytics:Lcom/google/firebase/analytics/FirebaseAnalytics;

    if-nez p3, :cond_0

    new-instance p3, Landroid/os/Bundle;

    invoke-direct {p3}, Landroid/os/Bundle;-><init>()V

    :cond_0
    invoke-direct {p0, p3, p2}, Lms/GmsAnalytics;->appendInfo(Landroid/os/Bundle;Ljava/lang/String;)Landroid/os/Bundle;

    move-result-object p2

    invoke-virtual {v0, p1, p2}, Lcom/google/firebase/analytics/FirebaseAnalytics;->logEvent(Ljava/lang/String;Landroid/os/Bundle;)V

    return-void
.end method

.method public setRemoteProperties(Ljava/util/Map;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    const-string v0, "properties"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 27
    iget-object v0, p0, Lms/GmsAnalytics;->firebaseAnalytics:Lcom/google/firebase/analytics/FirebaseAnalytics;

    .line 64
    invoke-interface {p1}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    move-result-object p1

    invoke-interface {p1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p1

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/util/Map$Entry;

    .line 29
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    invoke-virtual {v0, v2, v1}, Lcom/google/firebase/analytics/FirebaseAnalytics;->setUserProperty(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    return-void
.end method

.method public setRemoteProperty(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    const-string v0, "key"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "value"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 35
    iget-object v0, p0, Lms/GmsAnalytics;->firebaseAnalytics:Lcom/google/firebase/analytics/FirebaseAnalytics;

    .line 36
    invoke-virtual {v0, p1, p2}, Lcom/google/firebase/analytics/FirebaseAnalytics;->setUserProperty(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method
