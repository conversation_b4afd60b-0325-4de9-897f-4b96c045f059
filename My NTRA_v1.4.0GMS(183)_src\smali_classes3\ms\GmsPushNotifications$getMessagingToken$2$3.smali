.class final Lms/GmsPushNotifications$getMessagingToken$2$3;
.super Ljava/lang/Object;
.source "GmsPushNotifications.kt"

# interfaces
.implements Lcom/google/android/gms/tasks/OnCanceledListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lms/GmsPushNotifications;->getMessagingToken(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0008\n\u0000\n\u0002\u0010\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001H\n\u00a2\u0006\u0002\u0008\u0002"
    }
    d2 = {
        "<anonymous>",
        "",
        "onCanceled"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic $cont:Lkotlin/coroutines/Continuation;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lkotlin/coroutines/Continuation<",
            "Ljava/lang/String;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic this$0:Lms/GmsPushNotifications;


# direct methods
.method constructor <init>(Lms/GmsPushNotifications;Lkotlin/coroutines/Continuation;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lms/GmsPushNotifications;",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Ljava/lang/String;",
            ">;)V"
        }
    .end annotation

    iput-object p1, p0, Lms/GmsPushNotifications$getMessagingToken$2$3;->this$0:Lms/GmsPushNotifications;

    iput-object p2, p0, Lms/GmsPushNotifications$getMessagingToken$2$3;->$cont:Lkotlin/coroutines/Continuation;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onCanceled()V
    .locals 6

    .line 56
    sget-object v0, Leg/gov/tra/util/AppLogger;->INSTANCE:Leg/gov/tra/util/AppLogger;

    iget-object v1, p0, Lms/GmsPushNotifications$getMessagingToken$2$3;->this$0:Lms/GmsPushNotifications;

    invoke-static {v1}, Lms/GmsPushNotifications;->access$getTAG$p(Lms/GmsPushNotifications;)Ljava/lang/String;

    move-result-object v1

    const-string v2, "Failed to get the token."

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    invoke-static/range {v0 .. v5}, Leg/gov/tra/util/AppLogger;->i$default(Leg/gov/tra/util/AppLogger;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;ILjava/lang/Object;)V

    .line 57
    iget-object v0, p0, Lms/GmsPushNotifications$getMessagingToken$2$3;->$cont:Lkotlin/coroutines/Continuation;

    sget-object v1, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    const-string v1, ""

    invoke-static {v1}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-interface {v0, v1}, Lkotlin/coroutines/Continuation;->resumeWith(Ljava/lang/Object;)V

    return-void
.end method
