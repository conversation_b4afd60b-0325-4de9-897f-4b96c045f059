.class public final Lms/GmsPushNotifications;
.super Ljava/lang/Object;
.source "GmsPushNotifications.kt"

# interfaces
.implements Leg/gov/tra/util/mobile_services/MsPushNotifications;


# annotations
.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0002\u0008\u0004\n\u0002\u0010\u0002\n\u0002\u0008\u0005\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0011\u0010\u0008\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\tJ\u0008\u0010\n\u001a\u00020\u000bH\u0016J\u0010\u0010\u000c\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0006H\u0016J\u0008\u0010\u000e\u001a\u00020\u000bH\u0016J\u0010\u0010\u000f\u001a\u00020\u000b2\u0006\u0010\r\u001a\u00020\u0006H\u0016R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\u0008\u0019\u00a8\u0006\u0010"
    }
    d2 = {
        "Lms/GmsPushNotifications;",
        "Leg/gov/tra/util/mobile_services/MsPushNotifications;",
        "context",
        "Landroid/content/Context;",
        "(Landroid/content/Context;)V",
        "TAG",
        "",
        "token",
        "getMessagingToken",
        "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;",
        "init",
        "",
        "subscribeToTopic",
        "topic",
        "subscribeToTopics",
        "unsubscribeFromTopic",
        "My_NTRA-1.4.0(183)_gmsRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final TAG:Ljava/lang/String;

.field private final context:Landroid/content/Context;

.field private token:Ljava/lang/String;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 13
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lms/GmsPushNotifications;->context:Landroid/content/Context;

    const-string p1, ""

    .line 15
    iput-object p1, p0, Lms/GmsPushNotifications;->token:Ljava/lang/String;

    const-string p1, "GmsPushNotifications"

    .line 16
    iput-object p1, p0, Lms/GmsPushNotifications;->TAG:Ljava/lang/String;

    return-void
.end method

.method public static final synthetic access$getTAG$p(Lms/GmsPushNotifications;)Ljava/lang/String;
    .locals 0

    .line 13
    iget-object p0, p0, Lms/GmsPushNotifications;->TAG:Ljava/lang/String;

    return-object p0
.end method

.method public static final synthetic access$setToken$p(Lms/GmsPushNotifications;Ljava/lang/String;)V
    .locals 0

    .line 13
    iput-object p1, p0, Lms/GmsPushNotifications;->token:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getMessagingToken(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lkotlin/coroutines/Continuation<",
            "-",
            "Ljava/lang/String;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 35
    new-instance v0, Lkotlin/coroutines/SafeContinuation;

    invoke-static {p1}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->intercepted(Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;

    move-result-object v1

    invoke-direct {v0, v1}, Lkotlin/coroutines/SafeContinuation;-><init>(Lkotlin/coroutines/Continuation;)V

    move-object v1, v0

    check-cast v1, Lkotlin/coroutines/Continuation;

    .line 36
    iget-object v2, p0, Lms/GmsPushNotifications;->token:Ljava/lang/String;

    check-cast v2, Ljava/lang/CharSequence;

    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    move-result v2

    if-lez v2, :cond_0

    const/4 v2, 0x1

    goto :goto_0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    if-eqz v2, :cond_1

    .line 37
    sget-object v2, Lkotlin/Result;->Companion:Lkotlin/Result$Companion;

    iget-object v2, p0, Lms/GmsPushNotifications;->token:Ljava/lang/String;

    invoke-static {v2}, Lkotlin/Result;->constructor-impl(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    invoke-interface {v1, v2}, Lkotlin/coroutines/Continuation;->resumeWith(Ljava/lang/Object;)V

    goto :goto_1

    .line 41
    :cond_1
    invoke-static {}, Lcom/google/firebase/messaging/FirebaseMessaging;->getInstance()Lcom/google/firebase/messaging/FirebaseMessaging;

    move-result-object v2

    invoke-virtual {v2}, Lcom/google/firebase/messaging/FirebaseMessaging;->getToken()Lcom/google/android/gms/tasks/Task;

    move-result-object v2

    .line 42
    new-instance v3, Lms/GmsPushNotifications$getMessagingToken$2$1;

    invoke-direct {v3, p0, v1}, Lms/GmsPushNotifications$getMessagingToken$2$1;-><init>(Lms/GmsPushNotifications;Lkotlin/coroutines/Continuation;)V

    check-cast v3, Lkotlin/jvm/functions/Function1;

    new-instance v4, Lms/GmsPushNotifications$sam$com_google_android_gms_tasks_OnSuccessListener$0;

    invoke-direct {v4, v3}, Lms/GmsPushNotifications$sam$com_google_android_gms_tasks_OnSuccessListener$0;-><init>(Lkotlin/jvm/functions/Function1;)V

    check-cast v4, Lcom/google/android/gms/tasks/OnSuccessListener;

    invoke-virtual {v2, v4}, Lcom/google/android/gms/tasks/Task;->addOnSuccessListener(Lcom/google/android/gms/tasks/OnSuccessListener;)Lcom/google/android/gms/tasks/Task;

    move-result-object v2

    .line 51
    new-instance v3, Lms/GmsPushNotifications$getMessagingToken$2$2;

    invoke-direct {v3, p0, v1}, Lms/GmsPushNotifications$getMessagingToken$2$2;-><init>(Lms/GmsPushNotifications;Lkotlin/coroutines/Continuation;)V

    check-cast v3, Lcom/google/android/gms/tasks/OnFailureListener;

    invoke-virtual {v2, v3}, Lcom/google/android/gms/tasks/Task;->addOnFailureListener(Lcom/google/android/gms/tasks/OnFailureListener;)Lcom/google/android/gms/tasks/Task;

    move-result-object v2

    .line 55
    new-instance v3, Lms/GmsPushNotifications$getMessagingToken$2$3;

    invoke-direct {v3, p0, v1}, Lms/GmsPushNotifications$getMessagingToken$2$3;-><init>(Lms/GmsPushNotifications;Lkotlin/coroutines/Continuation;)V

    check-cast v3, Lcom/google/android/gms/tasks/OnCanceledListener;

    invoke-virtual {v2, v3}, Lcom/google/android/gms/tasks/Task;->addOnCanceledListener(Lcom/google/android/gms/tasks/OnCanceledListener;)Lcom/google/android/gms/tasks/Task;

    .line 35
    :goto_1
    invoke-virtual {v0}, Lkotlin/coroutines/SafeContinuation;->getOrThrow()Ljava/lang/Object;

    move-result-object v0

    invoke-static {}, Lkotlin/coroutines/intrinsics/IntrinsicsKt;->getCOROUTINE_SUSPENDED()Ljava/lang/Object;

    move-result-object v1

    if-ne v0, v1, :cond_2

    invoke-static {p1}, Lkotlin/coroutines/jvm/internal/DebugProbesKt;->probeCoroutineSuspended(Lkotlin/coroutines/Continuation;)V

    :cond_2
    return-object v0
.end method

.method public init()V
    .locals 2

    .line 19
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    iget-object v1, p0, Lms/GmsPushNotifications;->context:Landroid/content/Context;

    invoke-static {v0, v1}, Lcom/google/firebase/ktx/FirebaseKt;->initialize(Lcom/google/firebase/ktx/Firebase;Landroid/content/Context;)Lcom/google/firebase/FirebaseApp;

    return-void
.end method

.method public subscribeToTopic(Ljava/lang/String;)V
    .locals 1

    const-string v0, "topic"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 28
    invoke-static {}, Lcom/google/firebase/messaging/FirebaseMessaging;->getInstance()Lcom/google/firebase/messaging/FirebaseMessaging;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/messaging/FirebaseMessaging;->subscribeToTopic(Ljava/lang/String;)Lcom/google/android/gms/tasks/Task;

    return-void
.end method

.method public subscribeToTopics()V
    .locals 1

    .line 23
    sget-object v0, Leg/gov/tra/util/Constants;->INSTANCE:Leg/gov/tra/util/Constants;

    invoke-virtual {v0}, Leg/gov/tra/util/Constants;->getFIREBASE_TOPIC_ALL()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lms/GmsPushNotifications;->subscribeToTopic(Ljava/lang/String;)V

    .line 24
    sget-object v0, Leg/gov/tra/util/Constants;->INSTANCE:Leg/gov/tra/util/Constants;

    invoke-virtual {v0}, Leg/gov/tra/util/Constants;->getFIREBASE_TOPIC_ANDROID()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Lms/GmsPushNotifications;->subscribeToTopic(Ljava/lang/String;)V

    return-void
.end method

.method public unsubscribeFromTopic(Ljava/lang/String;)V
    .locals 1

    const-string v0, "topic"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 32
    invoke-static {}, Lcom/google/firebase/messaging/FirebaseMessaging;->getInstance()Lcom/google/firebase/messaging/FirebaseMessaging;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/messaging/FirebaseMessaging;->unsubscribeFromTopic(Ljava/lang/String;)Lcom/google/android/gms/tasks/Task;

    return-void
.end method
