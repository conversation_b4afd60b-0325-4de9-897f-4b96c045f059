.class public final synthetic Lms/GmsRemoteConfig$$ExternalSyntheticLambda0;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Lcom/google/android/gms/tasks/OnCompleteListener;


# instance fields
.field public final synthetic f$0:Lms/GmsRemoteConfig;


# direct methods
.method public synthetic constructor <init>(Lms/GmsRemoteConfig;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lms/GmsRemoteConfig$$ExternalSyntheticLambda0;->f$0:Lms/GmsRemoteConfig;

    return-void
.end method


# virtual methods
.method public final onComplete(Lcom/google/android/gms/tasks/Task;)V
    .locals 1

    iget-object v0, p0, Lms/GmsRemoteConfig$$ExternalSyntheticLambda0;->f$0:Lms/GmsRemoteConfig;

    invoke-static {v0, p1}, Lms/GmsRemoteConfig;->$r8$lambda$0Fu96pz8i5o1XECTnhUF_mIvLTY(Lms/GmsRemoteConfig;Lcom/google/android/gms/tasks/Task;)V

    return-void
.end method
