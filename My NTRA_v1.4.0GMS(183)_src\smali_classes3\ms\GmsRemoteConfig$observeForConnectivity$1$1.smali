.class final Lms/GmsRemoteConfig$observeForConnectivity$1$1;
.super Lkotlin/jvm/internal/Lambda;
.source "GmsRemoteConfig.kt"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lms/GmsRemoteConfig$observeForConnectivity$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Leg/gov/tra/util/network/Event;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0010\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\u0010\u0000\u001a\u00020\u00012\u000e\u0010\u0002\u001a\n \u0004*\u0004\u0018\u00010\u00030\u0003H\n\u00a2\u0006\u0002\u0008\u0005"
    }
    d2 = {
        "<anonymous>",
        "",
        "it",
        "Leg/gov/tra/util/network/Event;",
        "kotlin.jvm.PlatformType",
        "invoke"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field final synthetic this$0:Lms/GmsRemoteConfig;


# direct methods
.method constructor <init>(Lms/GmsRemoteConfig;)V
    .locals 0

    iput-object p1, p0, Lms/GmsRemoteConfig$observeForConnectivity$1$1;->this$0:Lms/GmsRemoteConfig;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 74
    check-cast p1, Leg/gov/tra/util/network/Event;

    invoke-virtual {p0, p1}, Lms/GmsRemoteConfig$observeForConnectivity$1$1;->invoke(Leg/gov/tra/util/network/Event;)V

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Leg/gov/tra/util/network/Event;)V
    .locals 2

    .line 75
    iget-object v0, p0, Lms/GmsRemoteConfig$observeForConnectivity$1$1;->this$0:Lms/GmsRemoteConfig;

    const/4 v1, 0x1

    invoke-static {v0, v1}, Lms/GmsRemoteConfig;->access$setNetworkObserved$p(Lms/GmsRemoteConfig;Z)V

    .line 76
    instance-of p1, p1, Leg/gov/tra/util/network/Event$ConnectivityAvailable;

    if-eqz p1, :cond_0

    .line 77
    iget-object p1, p0, Lms/GmsRemoteConfig$observeForConnectivity$1$1;->this$0:Lms/GmsRemoteConfig;

    invoke-virtual {p1}, Lms/GmsRemoteConfig;->fetch()V

    :cond_0
    return-void
.end method
