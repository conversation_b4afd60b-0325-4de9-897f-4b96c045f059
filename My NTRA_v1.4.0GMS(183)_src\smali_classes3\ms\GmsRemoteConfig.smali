.class public final Lms/GmsRemoteConfig;
.super Ljava/lang/Object;
.source "GmsRemoteConfig.kt"

# interfaces
.implements Leg/gov/tra/util/mobile_services/MsRemoteConfig;


# annotations
.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nGmsRemoteConfig.kt\nKotlin\n*S Kotlin\n*F\n+ 1 GmsRemoteConfig.kt\nms/GmsRemoteConfig\n+ 2 fake.kt\nkotlin/jvm/internal/FakeKt\n*L\n1#1,83:1\n1#2:84\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\u0008\u0004\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0008\u0008\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u0008J\u0008\u0010\u000f\u001a\u00020\u0010H\u0016J\u0010\u0010\u0011\u001a\u00020\u000c2\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J\u0010\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0012\u001a\u00020\u0013H\u0016J\u0008\u0010\u0019\u001a\u00020\u0010H\u0002J\u001e\u0010\u001a\u001a\u00020\u00102\u0014\u0010\u001b\u001a\u0010\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u001d\u0018\u00010\u001cH\u0016R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u000cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0012\u0010\r\u001a\u0004\u0018\u00010\u000cX\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\u000e\u00a8\u0006\u001e"
    }
    d2 = {
        "Lms/GmsRemoteConfig;",
        "Leg/gov/tra/util/mobile_services/MsRemoteConfig;",
        "interceptor",
        "Leg/gov/tra/data/remote/NetworkConnectionInterceptor;",
        "mobileServiceHelper",
        "Leg/gov/tra/util/mobile_services/MobileServiceHelper;",
        "dispatcher",
        "Lkotlinx/coroutines/CoroutineDispatcher;",
        "(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;Lkotlinx/coroutines/CoroutineDispatcher;)V",
        "configSettings",
        "Lcom/google/firebase/remoteconfig/FirebaseRemoteConfigSettings;",
        "networkObserved",
        "",
        "updated",
        "Ljava/lang/Boolean;",
        "fetch",
        "",
        "getBoolean",
        "key",
        "",
        "getDouble",
        "",
        "getLong",
        "",
        "getString",
        "observeForConnectivity",
        "setup",
        "defaults",
        "",
        "",
        "My_NTRA-1.4.0(183)_gmsRelease"
    }
    k = 0x1
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# instance fields
.field private final configSettings:Lcom/google/firebase/remoteconfig/FirebaseRemoteConfigSettings;

.field private final dispatcher:Lkotlinx/coroutines/CoroutineDispatcher;

.field private final interceptor:Leg/gov/tra/data/remote/NetworkConnectionInterceptor;

.field private final mobileServiceHelper:Leg/gov/tra/util/mobile_services/MobileServiceHelper;

.field private networkObserved:Z

.field private updated:Ljava/lang/Boolean;


# direct methods
.method public static synthetic $r8$lambda$0Fu96pz8i5o1XECTnhUF_mIvLTY(Lms/GmsRemoteConfig;Lcom/google/android/gms/tasks/Task;)V
    .locals 0

    invoke-static {p0, p1}, Lms/GmsRemoteConfig;->fetch$lambda$1(Lms/GmsRemoteConfig;Lcom/google/android/gms/tasks/Task;)V

    return-void
.end method

.method public constructor <init>(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;Lkotlinx/coroutines/CoroutineDispatcher;)V
    .locals 1

    const-string v0, "interceptor"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "mobileServiceHelper"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "dispatcher"

    invoke-static {p3, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 19
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 20
    iput-object p1, p0, Lms/GmsRemoteConfig;->interceptor:Leg/gov/tra/data/remote/NetworkConnectionInterceptor;

    .line 21
    iput-object p2, p0, Lms/GmsRemoteConfig;->mobileServiceHelper:Leg/gov/tra/util/mobile_services/MobileServiceHelper;

    .line 22
    iput-object p3, p0, Lms/GmsRemoteConfig;->dispatcher:Lkotlinx/coroutines/CoroutineDispatcher;

    const/4 p1, 0x0

    .line 26
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    iput-object p1, p0, Lms/GmsRemoteConfig;->updated:Ljava/lang/Boolean;

    .line 27
    sget-object p1, Lms/GmsRemoteConfig$configSettings$1;->INSTANCE:Lms/GmsRemoteConfig$configSettings$1;

    check-cast p1, Lkotlin/jvm/functions/Function1;

    invoke-static {p1}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->remoteConfigSettings(Lkotlin/jvm/functions/Function1;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfigSettings;

    move-result-object p1

    iput-object p1, p0, Lms/GmsRemoteConfig;->configSettings:Lcom/google/firebase/remoteconfig/FirebaseRemoteConfigSettings;

    return-void
.end method

.method public synthetic constructor <init>(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;Lkotlinx/coroutines/CoroutineDispatcher;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
    .locals 0

    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_0

    .line 22
    invoke-static {}, Lkotlinx/coroutines/Dispatchers;->getMain()Lkotlinx/coroutines/MainCoroutineDispatcher;

    move-result-object p3

    check-cast p3, Lkotlinx/coroutines/CoroutineDispatcher;

    .line 19
    :cond_0
    invoke-direct {p0, p1, p2, p3}, Lms/GmsRemoteConfig;-><init>(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;Lkotlinx/coroutines/CoroutineDispatcher;)V

    return-void
.end method

.method public static final synthetic access$setNetworkObserved$p(Lms/GmsRemoteConfig;Z)V
    .locals 0

    .line 19
    iput-boolean p1, p0, Lms/GmsRemoteConfig;->networkObserved:Z

    return-void
.end method

.method private static final fetch$lambda$1(Lms/GmsRemoteConfig;Lcom/google/android/gms/tasks/Task;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "task"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 46
    invoke-virtual {p1}, Lcom/google/android/gms/tasks/Task;->isSuccessful()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 47
    invoke-virtual {p1}, Lcom/google/android/gms/tasks/Task;->getResult()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Boolean;

    iput-object p1, p0, Lms/GmsRemoteConfig;->updated:Ljava/lang/Boolean;

    .line 48
    iget-object p1, p0, Lms/GmsRemoteConfig;->interceptor:Leg/gov/tra/data/remote/NetworkConnectionInterceptor;

    const-string v0, "base_url"

    invoke-virtual {p0, v0}, Lms/GmsRemoteConfig;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Leg/gov/tra/data/remote/NetworkConnectionInterceptor;->setHost(Ljava/lang/String;)V

    goto :goto_0

    .line 50
    :cond_0
    invoke-direct {p0}, Lms/GmsRemoteConfig;->observeForConnectivity()V

    :goto_0
    return-void
.end method

.method private final observeForConnectivity()V
    .locals 7

    .line 72
    iget-boolean v0, p0, Lms/GmsRemoteConfig;->networkObserved:Z

    if-nez v0, :cond_0

    .line 73
    sget-object v0, Lkotlinx/coroutines/GlobalScope;->INSTANCE:Lkotlinx/coroutines/GlobalScope;

    move-object v1, v0

    check-cast v1, Lkotlinx/coroutines/CoroutineScope;

    iget-object v0, p0, Lms/GmsRemoteConfig;->dispatcher:Lkotlinx/coroutines/CoroutineDispatcher;

    move-object v2, v0

    check-cast v2, Lkotlin/coroutines/CoroutineContext;

    const/4 v3, 0x0

    new-instance v0, Lms/GmsRemoteConfig$observeForConnectivity$1;

    const/4 v4, 0x0

    invoke-direct {v0, p0, v4}, Lms/GmsRemoteConfig$observeForConnectivity$1;-><init>(Lms/GmsRemoteConfig;Lkotlin/coroutines/Continuation;)V

    move-object v4, v0

    check-cast v4, Lkotlin/jvm/functions/Function2;

    const/4 v5, 0x2

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lkotlinx/coroutines/BuildersKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;

    :cond_0
    return-void
.end method


# virtual methods
.method public fetch()V
    .locals 2

    .line 44
    sget-object v0, Leg/gov/tra/util/network/NetworkStateHolder;->INSTANCE:Leg/gov/tra/util/network/NetworkStateHolder;

    invoke-virtual {v0}, Leg/gov/tra/util/network/NetworkStateHolder;->isConnected()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 45
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    invoke-virtual {v0}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->fetchAndActivate()Lcom/google/android/gms/tasks/Task;

    move-result-object v0

    new-instance v1, Lms/GmsRemoteConfig$$ExternalSyntheticLambda0;

    invoke-direct {v1, p0}, Lms/GmsRemoteConfig$$ExternalSyntheticLambda0;-><init>(Lms/GmsRemoteConfig;)V

    invoke-virtual {v0, v1}, Lcom/google/android/gms/tasks/Task;->addOnCompleteListener(Lcom/google/android/gms/tasks/OnCompleteListener;)Lcom/google/android/gms/tasks/Task;

    goto :goto_0

    .line 54
    :cond_0
    invoke-direct {p0}, Lms/GmsRemoteConfig;->observeForConnectivity()V

    :goto_0
    return-void
.end method

.method public getBoolean(Ljava/lang/String;)Z
    .locals 1

    const-string v0, "key"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 63
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->getBoolean(Ljava/lang/String;)Z

    move-result p1

    return p1
.end method

.method public getDouble(Ljava/lang/String;)D
    .locals 2

    const-string v0, "key"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 69
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->getDouble(Ljava/lang/String;)D

    move-result-wide v0

    return-wide v0
.end method

.method public getLong(Ljava/lang/String;)J
    .locals 2

    const-string v0, "key"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 66
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->getLong(Ljava/lang/String;)J

    move-result-wide v0

    return-wide v0
.end method

.method public getString(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    const-string v0, "key"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 60
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "getString(...)"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p1, Ljava/lang/CharSequence;

    invoke-static {p1}, Lkotlin/text/StringsKt;->trim(Ljava/lang/CharSequence;)Ljava/lang/CharSequence;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public setup(Ljava/util/Map;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/Map<",
            "Ljava/lang/String;",
            "+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    .line 30
    iget-object v0, p0, Lms/GmsRemoteConfig;->mobileServiceHelper:Leg/gov/tra/util/mobile_services/MobileServiceHelper;

    invoke-interface {v0}, Leg/gov/tra/util/mobile_services/MobileServiceHelper;->isAvailable()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 32
    sget-object v0, Leg/gov/tra/util/Utils;->INSTANCE:Leg/gov/tra/util/Utils;

    invoke-virtual {v0}, Leg/gov/tra/util/Utils;->isDebugOrQc()Z

    move-result v0

    if-eqz v0, :cond_0

    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    iget-object v1, p0, Lms/GmsRemoteConfig;->configSettings:Lcom/google/firebase/remoteconfig/FirebaseRemoteConfigSettings;

    invoke-virtual {v0, v1}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->setConfigSettingsAsync(Lcom/google/firebase/remoteconfig/FirebaseRemoteConfigSettings;)Lcom/google/android/gms/tasks/Task;

    :cond_0
    if-eqz p1, :cond_1

    .line 33
    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/remoteconfig/ktx/RemoteConfigKt;->getRemoteConfig(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/google/firebase/remoteconfig/FirebaseRemoteConfig;->setDefaultsAsync(Ljava/util/Map;)Lcom/google/android/gms/tasks/Task;

    .line 34
    :cond_1
    invoke-virtual {p0}, Lms/GmsRemoteConfig;->fetch()V

    :cond_2
    return-void
.end method
