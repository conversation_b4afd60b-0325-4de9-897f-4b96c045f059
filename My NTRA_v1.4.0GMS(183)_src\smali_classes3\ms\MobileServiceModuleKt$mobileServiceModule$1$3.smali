.class final Lms/MobileServiceModuleKt$mobileServiceModule$1$3;
.super Lkotlin/jvm/internal/Lambda;
.source "MobileServiceModule.kt"

# interfaces
.implements Lkotlin/jvm/functions/Function2;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lms/MobileServiceModuleKt$mobileServiceModule$1;->invoke(Lorg/koin/core/module/Module;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function2<",
        "Lorg/koin/core/scope/Scope;",
        "Lorg/koin/core/parameter/ParametersHolder;",
        "Leg/gov/tra/util/mobile_services/MsAnalytics;",
        ">;"
    }
.end annotation

.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nMobileServiceModule.kt\nKotlin\n*S Kotlin\n*F\n+ 1 MobileServiceModule.kt\nms/MobileServiceModuleKt$mobileServiceModule$1$3\n+ 2 Scope.kt\norg/koin/core/scope/Scope\n*L\n1#1,46:1\n127#2,5:47\n*S KotlinDebug\n*F\n+ 1 MobileServiceModule.kt\nms/MobileServiceModuleKt$mobileServiceModule$1$3\n*L\n40#1:47,5\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0004H\n\u00a2\u0006\u0002\u0008\u0005"
    }
    d2 = {
        "<anonymous>",
        "Leg/gov/tra/util/mobile_services/MsAnalytics;",
        "Lorg/koin/core/scope/Scope;",
        "it",
        "Lorg/koin/core/parameter/ParametersHolder;",
        "invoke"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1$3;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lms/MobileServiceModuleKt$mobileServiceModule$1$3;

    invoke-direct {v0}, Lms/MobileServiceModuleKt$mobileServiceModule$1$3;-><init>()V

    sput-object v0, Lms/MobileServiceModuleKt$mobileServiceModule$1$3;->INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1$3;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x2

    invoke-direct {p0, v0}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final invoke(Lorg/koin/core/scope/Scope;Lorg/koin/core/parameter/ParametersHolder;)Leg/gov/tra/util/mobile_services/MsAnalytics;
    .locals 1

    const-string v0, "$this$single"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "it"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 51
    const-class p2, Leg/gov/tra/data/local/pref/SecurePrefDataSource;

    invoke-static {p2}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object p2

    const/4 v0, 0x0

    invoke-virtual {p1, p2, v0, v0}, Lorg/koin/core/scope/Scope;->get(Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lkotlin/jvm/functions/Function0;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Leg/gov/tra/data/local/pref/SecurePrefDataSource;

    .line 40
    invoke-static {p1}, Lms/MobileServiceModuleKt$mobileServiceModule$1;->access$invoke$provideAnalytics(Leg/gov/tra/data/local/pref/SecurePrefDataSource;)Leg/gov/tra/util/mobile_services/MsAnalytics;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 40
    check-cast p1, Lorg/koin/core/scope/Scope;

    check-cast p2, Lorg/koin/core/parameter/ParametersHolder;

    invoke-virtual {p0, p1, p2}, Lms/MobileServiceModuleKt$mobileServiceModule$1$3;->invoke(Lorg/koin/core/scope/Scope;Lorg/koin/core/parameter/ParametersHolder;)Leg/gov/tra/util/mobile_services/MsAnalytics;

    move-result-object p1

    return-object p1
.end method
