.class final Lms/MobileServiceModuleKt$mobileServiceModule$1;
.super Lkotlin/jvm/internal/Lambda;
.source "MobileServiceModule.kt"

# interfaces
.implements Lkotlin/jvm/functions/Function1;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lms/MobileServiceModuleKt;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lkotlin/jvm/internal/Lambda;",
        "Lkotlin/jvm/functions/Function1<",
        "Lorg/koin/core/module/Module;",
        "Lkotlin/Unit;",
        ">;"
    }
.end annotation

.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nMobileServiceModule.kt\nKotlin\n*S Kotlin\n*F\n+ 1 MobileServiceModule.kt\nms/MobileServiceModuleKt$mobileServiceModule$1\n+ 2 Module.kt\norg/koin/core/module/Module\n+ 3 BeanDefinition.kt\norg/koin/core/definition/BeanDefinitionKt\n*L\n1#1,46:1\n71#2,6:47\n77#2,7:67\n71#2,6:74\n77#2,7:94\n71#2,6:101\n77#2,7:121\n71#2,6:128\n77#2,7:148\n99#3,14:53\n99#3,14:80\n99#3,14:107\n99#3,14:134\n*S KotlinDebug\n*F\n+ 1 MobileServiceModule.kt\nms/MobileServiceModuleKt$mobileServiceModule$1\n*L\n38#1:47,6\n38#1:67,7\n39#1:74,6\n39#1:94,7\n40#1:101,6\n40#1:121,7\n41#1:128,6\n41#1:148,7\n38#1:53,14\n39#1:80,14\n40#1:107,14\n41#1:134,14\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\n\u00a2\u0006\u0002\u0008\u0003"
    }
    d2 = {
        "<anonymous>",
        "",
        "Lorg/koin/core/module/Module;",
        "invoke"
    }
    k = 0x3
    mv = {
        0x1,
        0x9,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field public static final INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lms/MobileServiceModuleKt$mobileServiceModule$1;

    invoke-direct {v0}, Lms/MobileServiceModuleKt$mobileServiceModule$1;-><init>()V

    sput-object v0, Lms/MobileServiceModuleKt$mobileServiceModule$1;->INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lkotlin/jvm/internal/Lambda;-><init>(I)V

    return-void
.end method

.method public static final synthetic access$invoke$provideAnalytics(Leg/gov/tra/data/local/pref/SecurePrefDataSource;)Leg/gov/tra/util/mobile_services/MsAnalytics;
    .locals 0

    .line 16
    invoke-static {p0}, Lms/MobileServiceModuleKt$mobileServiceModule$1;->invoke$provideAnalytics(Leg/gov/tra/data/local/pref/SecurePrefDataSource;)Leg/gov/tra/util/mobile_services/MsAnalytics;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic access$invoke$provideMobileService(Landroid/content/Context;)Leg/gov/tra/util/mobile_services/MobileServiceHelper;
    .locals 0

    .line 16
    invoke-static {p0}, Lms/MobileServiceModuleKt$mobileServiceModule$1;->invoke$provideMobileService(Landroid/content/Context;)Leg/gov/tra/util/mobile_services/MobileServiceHelper;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic access$invoke$providePushNotifications(Landroid/content/Context;)Leg/gov/tra/util/mobile_services/MsPushNotifications;
    .locals 0

    .line 16
    invoke-static {p0}, Lms/MobileServiceModuleKt$mobileServiceModule$1;->invoke$providePushNotifications(Landroid/content/Context;)Leg/gov/tra/util/mobile_services/MsPushNotifications;

    move-result-object p0

    return-object p0
.end method

.method public static final synthetic access$invoke$provideRemoteConfig(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;)Leg/gov/tra/util/mobile_services/MsRemoteConfig;
    .locals 0

    .line 16
    invoke-static {p0, p1}, Lms/MobileServiceModuleKt$mobileServiceModule$1;->invoke$provideRemoteConfig(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;)Leg/gov/tra/util/mobile_services/MsRemoteConfig;

    move-result-object p0

    return-object p0
.end method

.method private static final invoke$provideAnalytics(Leg/gov/tra/data/local/pref/SecurePrefDataSource;)Leg/gov/tra/util/mobile_services/MsAnalytics;
    .locals 8

    .line 31
    new-instance v7, Lms/GmsAnalytics;

    sget-object v0, Lcom/google/firebase/ktx/Firebase;->INSTANCE:Lcom/google/firebase/ktx/Firebase;

    invoke-static {v0}, Lcom/google/firebase/analytics/ktx/AnalyticsKt;->getAnalytics(Lcom/google/firebase/ktx/Firebase;)Lcom/google/firebase/analytics/FirebaseAnalytics;

    move-result-object v1

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v5, 0xc

    const/4 v6, 0x0

    move-object v0, v7

    move-object v2, p0

    invoke-direct/range {v0 .. v6}, Lms/GmsAnalytics;-><init>(Lcom/google/firebase/analytics/FirebaseAnalytics;Leg/gov/tra/data/local/pref/SecurePrefDataSource;Leg/gov/tra/util/SessionManager;Lcom/google/gson/Gson;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    check-cast v7, Leg/gov/tra/util/mobile_services/MsAnalytics;

    return-object v7
.end method

.method private static final invoke$provideMobileService(Landroid/content/Context;)Leg/gov/tra/util/mobile_services/MobileServiceHelper;
    .locals 1

    .line 20
    new-instance v0, LGmsHelper;

    invoke-direct {v0, p0}, LGmsHelper;-><init>(Landroid/content/Context;)V

    check-cast v0, Leg/gov/tra/util/mobile_services/MobileServiceHelper;

    return-object v0
.end method

.method private static final invoke$providePushNotifications(Landroid/content/Context;)Leg/gov/tra/util/mobile_services/MsPushNotifications;
    .locals 1

    .line 35
    new-instance v0, Lms/GmsPushNotifications;

    invoke-direct {v0, p0}, Lms/GmsPushNotifications;-><init>(Landroid/content/Context;)V

    check-cast v0, Leg/gov/tra/util/mobile_services/MsPushNotifications;

    return-object v0
.end method

.method private static final invoke$provideRemoteConfig(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;)Leg/gov/tra/util/mobile_services/MsRemoteConfig;
    .locals 7

    .line 27
    new-instance v6, Lms/GmsRemoteConfig;

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    move-object v0, v6

    move-object v1, p0

    move-object v2, p1

    invoke-direct/range {v0 .. v5}, Lms/GmsRemoteConfig;-><init>(Leg/gov/tra/data/remote/NetworkConnectionInterceptor;Leg/gov/tra/util/mobile_services/MobileServiceHelper;Lkotlinx/coroutines/CoroutineDispatcher;ILkotlin/jvm/internal/DefaultConstructorMarker;)V

    check-cast v6, Leg/gov/tra/util/mobile_services/MsRemoteConfig;

    return-object v6
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 16
    check-cast p1, Lorg/koin/core/module/Module;

    invoke-virtual {p0, p1}, Lms/MobileServiceModuleKt$mobileServiceModule$1;->invoke(Lorg/koin/core/module/Module;)V

    sget-object p1, Lkotlin/Unit;->INSTANCE:Lkotlin/Unit;

    return-object p1
.end method

.method public final invoke(Lorg/koin/core/module/Module;)V
    .locals 11

    const-string v0, "$this$module"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 38
    sget-object v0, Lms/MobileServiceModuleKt$mobileServiceModule$1$1;->INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1$1;

    move-object v5, v0

    check-cast v5, Lkotlin/jvm/functions/Function2;

    .line 52
    sget-object v6, Lorg/koin/core/definition/Kind;->Singleton:Lorg/koin/core/definition/Kind;

    sget-object v0, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v0}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v0

    move-object v2, v0

    check-cast v2, Lorg/koin/core/qualifier/Qualifier;

    .line 57
    invoke-static {}, Lkotlin/collections/CollectionsKt;->emptyList()Ljava/util/List;

    move-result-object v7

    .line 60
    new-instance v0, Lorg/koin/core/definition/BeanDefinition;

    .line 61
    const-class v1, Leg/gov/tra/util/mobile_services/MobileServiceHelper;

    invoke-static {v1}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v3

    const/4 v4, 0x0

    move-object v1, v0

    .line 60
    invoke-direct/range {v1 .. v7}, Lorg/koin/core/definition/BeanDefinition;-><init>(Lorg/koin/core/qualifier/Qualifier;Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lkotlin/jvm/functions/Function2;Lorg/koin/core/definition/Kind;Ljava/util/List;)V

    .line 67
    invoke-virtual {v0}, Lorg/koin/core/definition/BeanDefinition;->getPrimaryType()Lkotlin/reflect/KClass;

    move-result-object v1

    sget-object v2, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v2}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v2

    check-cast v2, Lorg/koin/core/qualifier/Qualifier;

    const/4 v3, 0x0

    invoke-static {v1, v3, v2}, Lorg/koin/core/definition/BeanDefinitionKt;->indexKey(Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lorg/koin/core/qualifier/Qualifier;)Ljava/lang/String;

    move-result-object v5

    .line 68
    new-instance v1, Lorg/koin/core/instance/SingleInstanceFactory;

    invoke-direct {v1, v0}, Lorg/koin/core/instance/SingleInstanceFactory;-><init>(Lorg/koin/core/definition/BeanDefinition;)V

    .line 69
    move-object v6, v1

    check-cast v6, Lorg/koin/core/instance/InstanceFactory;

    const/4 v7, 0x0

    const/4 v8, 0x4

    const/4 v9, 0x0

    move-object v4, p1

    invoke-static/range {v4 .. v9}, Lorg/koin/core/module/Module;->saveMapping$default(Lorg/koin/core/module/Module;Ljava/lang/String;Lorg/koin/core/instance/InstanceFactory;ZILjava/lang/Object;)V

    .line 70
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getCreatedAtStart()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 71
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getEagerInstances()Ljava/util/HashSet;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 73
    :cond_0
    new-instance v0, Lkotlin/Pair;

    invoke-direct {v0, p1, v1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 39
    sget-object v0, Lms/MobileServiceModuleKt$mobileServiceModule$1$2;->INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1$2;

    move-object v8, v0

    check-cast v8, Lkotlin/jvm/functions/Function2;

    .line 79
    sget-object v9, Lorg/koin/core/definition/Kind;->Singleton:Lorg/koin/core/definition/Kind;

    sget-object v0, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v0}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Lorg/koin/core/qualifier/Qualifier;

    .line 84
    invoke-static {}, Lkotlin/collections/CollectionsKt;->emptyList()Ljava/util/List;

    move-result-object v10

    .line 87
    new-instance v0, Lorg/koin/core/definition/BeanDefinition;

    .line 88
    const-class v1, Leg/gov/tra/util/mobile_services/MsRemoteConfig;

    invoke-static {v1}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v6

    const/4 v7, 0x0

    move-object v4, v0

    .line 87
    invoke-direct/range {v4 .. v10}, Lorg/koin/core/definition/BeanDefinition;-><init>(Lorg/koin/core/qualifier/Qualifier;Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lkotlin/jvm/functions/Function2;Lorg/koin/core/definition/Kind;Ljava/util/List;)V

    .line 94
    invoke-virtual {v0}, Lorg/koin/core/definition/BeanDefinition;->getPrimaryType()Lkotlin/reflect/KClass;

    move-result-object v1

    sget-object v2, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v2}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v2

    check-cast v2, Lorg/koin/core/qualifier/Qualifier;

    invoke-static {v1, v3, v2}, Lorg/koin/core/definition/BeanDefinitionKt;->indexKey(Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lorg/koin/core/qualifier/Qualifier;)Ljava/lang/String;

    move-result-object v5

    .line 95
    new-instance v1, Lorg/koin/core/instance/SingleInstanceFactory;

    invoke-direct {v1, v0}, Lorg/koin/core/instance/SingleInstanceFactory;-><init>(Lorg/koin/core/definition/BeanDefinition;)V

    .line 96
    move-object v6, v1

    check-cast v6, Lorg/koin/core/instance/InstanceFactory;

    const/4 v7, 0x0

    const/4 v8, 0x4

    const/4 v9, 0x0

    move-object v4, p1

    invoke-static/range {v4 .. v9}, Lorg/koin/core/module/Module;->saveMapping$default(Lorg/koin/core/module/Module;Ljava/lang/String;Lorg/koin/core/instance/InstanceFactory;ZILjava/lang/Object;)V

    .line 97
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getCreatedAtStart()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 98
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getEagerInstances()Ljava/util/HashSet;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 100
    :cond_1
    new-instance v0, Lkotlin/Pair;

    invoke-direct {v0, p1, v1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 40
    sget-object v0, Lms/MobileServiceModuleKt$mobileServiceModule$1$3;->INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1$3;

    move-object v8, v0

    check-cast v8, Lkotlin/jvm/functions/Function2;

    .line 106
    sget-object v9, Lorg/koin/core/definition/Kind;->Singleton:Lorg/koin/core/definition/Kind;

    sget-object v0, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v0}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Lorg/koin/core/qualifier/Qualifier;

    .line 111
    invoke-static {}, Lkotlin/collections/CollectionsKt;->emptyList()Ljava/util/List;

    move-result-object v10

    .line 114
    new-instance v0, Lorg/koin/core/definition/BeanDefinition;

    .line 115
    const-class v1, Leg/gov/tra/util/mobile_services/MsAnalytics;

    invoke-static {v1}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v6

    const/4 v7, 0x0

    move-object v4, v0

    .line 114
    invoke-direct/range {v4 .. v10}, Lorg/koin/core/definition/BeanDefinition;-><init>(Lorg/koin/core/qualifier/Qualifier;Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lkotlin/jvm/functions/Function2;Lorg/koin/core/definition/Kind;Ljava/util/List;)V

    .line 121
    invoke-virtual {v0}, Lorg/koin/core/definition/BeanDefinition;->getPrimaryType()Lkotlin/reflect/KClass;

    move-result-object v1

    sget-object v2, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v2}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v2

    check-cast v2, Lorg/koin/core/qualifier/Qualifier;

    invoke-static {v1, v3, v2}, Lorg/koin/core/definition/BeanDefinitionKt;->indexKey(Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lorg/koin/core/qualifier/Qualifier;)Ljava/lang/String;

    move-result-object v5

    .line 122
    new-instance v1, Lorg/koin/core/instance/SingleInstanceFactory;

    invoke-direct {v1, v0}, Lorg/koin/core/instance/SingleInstanceFactory;-><init>(Lorg/koin/core/definition/BeanDefinition;)V

    .line 123
    move-object v6, v1

    check-cast v6, Lorg/koin/core/instance/InstanceFactory;

    const/4 v7, 0x0

    const/4 v8, 0x4

    const/4 v9, 0x0

    move-object v4, p1

    invoke-static/range {v4 .. v9}, Lorg/koin/core/module/Module;->saveMapping$default(Lorg/koin/core/module/Module;Ljava/lang/String;Lorg/koin/core/instance/InstanceFactory;ZILjava/lang/Object;)V

    .line 124
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getCreatedAtStart()Z

    move-result v0

    if-eqz v0, :cond_2

    .line 125
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getEagerInstances()Ljava/util/HashSet;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 127
    :cond_2
    new-instance v0, Lkotlin/Pair;

    invoke-direct {v0, p1, v1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 41
    sget-object v0, Lms/MobileServiceModuleKt$mobileServiceModule$1$4;->INSTANCE:Lms/MobileServiceModuleKt$mobileServiceModule$1$4;

    move-object v8, v0

    check-cast v8, Lkotlin/jvm/functions/Function2;

    .line 133
    sget-object v9, Lorg/koin/core/definition/Kind;->Singleton:Lorg/koin/core/definition/Kind;

    sget-object v0, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v0}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v0

    move-object v5, v0

    check-cast v5, Lorg/koin/core/qualifier/Qualifier;

    .line 138
    invoke-static {}, Lkotlin/collections/CollectionsKt;->emptyList()Ljava/util/List;

    move-result-object v10

    .line 141
    new-instance v0, Lorg/koin/core/definition/BeanDefinition;

    .line 142
    const-class v1, Leg/gov/tra/util/mobile_services/MsPushNotifications;

    invoke-static {v1}, Lkotlin/jvm/internal/Reflection;->getOrCreateKotlinClass(Ljava/lang/Class;)Lkotlin/reflect/KClass;

    move-result-object v6

    const/4 v7, 0x0

    move-object v4, v0

    .line 141
    invoke-direct/range {v4 .. v10}, Lorg/koin/core/definition/BeanDefinition;-><init>(Lorg/koin/core/qualifier/Qualifier;Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lkotlin/jvm/functions/Function2;Lorg/koin/core/definition/Kind;Ljava/util/List;)V

    .line 148
    invoke-virtual {v0}, Lorg/koin/core/definition/BeanDefinition;->getPrimaryType()Lkotlin/reflect/KClass;

    move-result-object v1

    sget-object v2, Lorg/koin/core/registry/ScopeRegistry;->Companion:Lorg/koin/core/registry/ScopeRegistry$Companion;

    invoke-virtual {v2}, Lorg/koin/core/registry/ScopeRegistry$Companion;->getRootScopeQualifier()Lorg/koin/core/qualifier/StringQualifier;

    move-result-object v2

    check-cast v2, Lorg/koin/core/qualifier/Qualifier;

    invoke-static {v1, v3, v2}, Lorg/koin/core/definition/BeanDefinitionKt;->indexKey(Lkotlin/reflect/KClass;Lorg/koin/core/qualifier/Qualifier;Lorg/koin/core/qualifier/Qualifier;)Ljava/lang/String;

    move-result-object v5

    .line 149
    new-instance v1, Lorg/koin/core/instance/SingleInstanceFactory;

    invoke-direct {v1, v0}, Lorg/koin/core/instance/SingleInstanceFactory;-><init>(Lorg/koin/core/definition/BeanDefinition;)V

    .line 150
    move-object v6, v1

    check-cast v6, Lorg/koin/core/instance/InstanceFactory;

    const/4 v7, 0x0

    const/4 v8, 0x4

    const/4 v9, 0x0

    move-object v4, p1

    invoke-static/range {v4 .. v9}, Lorg/koin/core/module/Module;->saveMapping$default(Lorg/koin/core/module/Module;Ljava/lang/String;Lorg/koin/core/instance/InstanceFactory;ZILjava/lang/Object;)V

    .line 151
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getCreatedAtStart()Z

    move-result v0

    if-eqz v0, :cond_3

    .line 152
    invoke-virtual {p1}, Lorg/koin/core/module/Module;->getEagerInstances()Ljava/util/HashSet;

    move-result-object v0

    invoke-virtual {v0, v1}, Ljava/util/HashSet;->add(Ljava/lang/Object;)Z

    .line 154
    :cond_3
    new-instance v0, Lkotlin/Pair;

    invoke-direct {v0, p1, v1}, Lkotlin/Pair;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    return-void
.end method
