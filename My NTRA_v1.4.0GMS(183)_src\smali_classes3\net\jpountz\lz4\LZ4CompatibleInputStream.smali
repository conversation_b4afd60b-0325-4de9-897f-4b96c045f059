.class public final Lnet/jpountz/lz4/LZ4CompatibleInputStream;
.super Ljava/io/FilterInputStream;
.source "LZ4CompatibleInputStream.java"


# instance fields
.field private error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

.field private inputAvailable:I

.field private inputBuffer:[B

.field private inputOffset:I

.field private lz4fContext:J

.field private outputAvailable:I

.field private outputBuffer:[B

.field private outputOffset:I


# direct methods
.method public constructor <init>(Ljava/io/InputStream;)V
    .locals 1
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/high16 v0, 0x10000

    .line 69
    invoke-direct {p0, p1, v0, v0}, Lnet/jpountz/lz4/LZ4CompatibleInputStream;-><init>(Ljava/io/InputStream;II)V

    return-void
.end method

.method public constructor <init>(Ljava/io/InputStream;II)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 58
    invoke-direct {p0, p1}, Ljava/io/FilterInputStream;-><init>(Ljava/io/InputStream;)V

    const/4 p1, 0x0

    .line 38
    iput p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    .line 39
    iput p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    .line 41
    iput p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    .line 42
    iput p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    .line 47
    new-instance p1, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-direct {p1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;-><init>()V

    iput-object p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    .line 60
    new-array p3, p3, [B

    iput-object p3, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    .line 61
    new-array p2, p2, [B

    iput-object p2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputBuffer:[B

    .line 64
    invoke-static {p1}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_createDecompressionContext(Lnet/jpountz/lz4/LZ4JNI$LZ4FError;)J

    move-result-wide p1

    iput-wide p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->lz4fContext:J

    .line 65
    iget-object p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {p1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    return-void
.end method

.method private refill()V
    .locals 12
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/4 v0, 0x0

    .line 142
    iput v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    .line 143
    iput v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    .line 146
    :goto_0
    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    array-length v2, v2

    if-ge v1, v2, :cond_2

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    const/4 v2, -0x1

    if-eq v1, v2, :cond_2

    .line 148
    iget v3, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    if-ne v3, v1, :cond_0

    .line 150
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->in:Ljava/io/InputStream;

    iget-object v3, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputBuffer:[B

    array-length v4, v3

    iget v5, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    sub-int/2addr v4, v5

    invoke-virtual {v1, v3, v0, v4}, Ljava/io/InputStream;->read([BII)I

    move-result v1

    iput v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    .line 151
    iput v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    if-ne v1, v2, :cond_0

    return-void

    .line 159
    :cond_0
    iget-wide v3, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->lz4fContext:J

    iget-object v5, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputBuffer:[B

    iget v6, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    sub-int v7, v1, v6

    iget-object v8, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    iget v9, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    array-length v1, v8

    sub-int v10, v1, v9

    iget-object v11, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-static/range {v3 .. v11}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_decompress(J[BII[BIILnet/jpountz/lz4/LZ4JNI$LZ4FError;)I

    move-result v1

    .line 163
    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v2}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    if-ltz v1, :cond_1

    .line 168
    iget v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    add-int/2addr v2, v1

    iput v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    .line 169
    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    iput v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    goto :goto_0

    :cond_1
    neg-int v1, v1

    add-int/lit8 v1, v1, -0x1

    .line 174
    iget v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    add-int/2addr v2, v1

    iput v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputOffset:I

    .line 175
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    array-length v1, v1

    iput v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    goto :goto_0

    :cond_2
    return-void
.end method


# virtual methods
.method public available()I
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 74
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    array-length v0, v0

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    sub-int/2addr v0, v1

    return v0
.end method

.method public close()V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 128
    invoke-super {p0}, Ljava/io/FilterInputStream;->close()V

    .line 130
    iget-wide v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->lz4fContext:J

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-static {v0, v1, v2}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_freeDecompressionContext(JLnet/jpountz/lz4/LZ4JNI$LZ4FError;)V

    .line 131
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v0}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    return-void
.end method

.method public mark(I)V
    .locals 0

    return-void
.end method

.method public markSupported()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public read()I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 80
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    if-ne v0, v1, :cond_0

    .line 81
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->refill()V

    .line 84
    :cond_0
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    iget v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    if-ne v0, v2, :cond_1

    return v1

    .line 89
    :cond_1
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    aget-byte v0, v0, v1

    and-int/lit16 v0, v0, 0xff

    return v0
.end method

.method public read([BII)I
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 94
    invoke-static {p1, p2, p3}, Lnet/jpountz/util/SafeUtils;->checkRange([BII)V

    .line 96
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    if-ne v0, v1, :cond_0

    .line 97
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->refill()V

    .line 100
    :cond_0
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    iget v2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    if-ne v0, v2, :cond_1

    return v1

    .line 103
    :cond_1
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    sub-int/2addr v0, v1

    invoke-static {p3, v0}, Ljava/lang/Math;->min(II)I

    move-result p3

    .line 104
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputBuffer:[B

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    invoke-static {v0, v1, p1, p2, p3}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 106
    iget p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    add-int/2addr p1, p3

    iput p1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    return p3
.end method

.method public reset()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 194
    new-instance v0, Ljava/io/IOException;

    const-string v1, "mark/reset not supported"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public skip(J)J
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 113
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    if-ne v0, v1, :cond_0

    .line 114
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->refill()V

    .line 117
    :cond_0
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->inputAvailable:I

    const/4 v1, -0x1

    if-ne v0, v1, :cond_1

    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    if-ne v0, v1, :cond_1

    const-wide/16 p1, -0x1

    return-wide p1

    .line 121
    :cond_1
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputAvailable:I

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    sub-int/2addr v0, v1

    int-to-long v0, v0

    invoke-static {p1, p2, v0, v1}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p1

    long-to-int p1, p1

    .line 122
    iget p2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    add-int/2addr p2, p1

    iput p2, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->outputOffset:I

    int-to-long p1, p1

    return-wide p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 199
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "(in="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleInputStream;->in:Ljava/io/InputStream;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
