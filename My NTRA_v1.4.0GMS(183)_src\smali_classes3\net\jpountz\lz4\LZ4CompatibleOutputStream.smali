.class public Lnet/jpountz/lz4/LZ4CompatibleOutputStream;
.super Ljava/io/FilterOutputStream;
.source "LZ4CompatibleOutputStream.java"


# instance fields
.field error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

.field inputBuffer:[B

.field inputOffset:I

.field lz4fContext:J

.field outputBuffer:[B


# direct methods
.method public constructor <init>(Ljava/io/OutputStream;)V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    const/high16 v0, 0x10000

    const/4 v1, 0x1

    .line 36
    invoke-direct {p0, p1, v0, v1}, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;-><init>(Ljava/io/OutputStream;II)V

    return-void
.end method

.method public constructor <init>(Ljava/io/OutputStream;II)V
    .locals 8
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 40
    invoke-direct {p0, p1}, Ljava/io/FilterOutputStream;-><init>(Ljava/io/OutputStream;)V

    const/4 v0, 0x0

    .line 27
    iput-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    const/4 v0, 0x0

    .line 30
    iput v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    .line 33
    new-instance v1, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-direct {v1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;-><init>()V

    iput-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    .line 42
    invoke-static {v1}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_createCompressionContext(Lnet/jpountz/lz4/LZ4JNI$LZ4FError;)J

    move-result-wide v1

    iput-wide v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->lz4fContext:J

    .line 43
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    .line 45
    new-array p2, p2, [B

    iput-object p2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputBuffer:[B

    const/16 p2, 0xf

    new-array v4, p2, [B

    .line 48
    iput-object v4, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    .line 51
    iget-wide v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->lz4fContext:J

    const/4 v5, 0x0

    array-length v6, v4

    iget-object v7, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    move v3, p3

    invoke-static/range {v1 .. v7}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_compressBegin(JI[BIILnet/jpountz/lz4/LZ4JNI$LZ4FError;)I

    move-result p2

    .line 52
    iget-object p3, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {p3}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    .line 55
    iget-object p3, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    invoke-virtual {p1, p3, v0, p2}, Ljava/io/OutputStream;->write([BII)V

    return-void
.end method

.method private compressBuffer()V
    .locals 10
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 64
    iget v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    invoke-static {v0}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_compressBound(I)I

    move-result v0

    .line 65
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    if-eqz v1, :cond_0

    array-length v1, v1

    if-ge v1, v0, :cond_1

    .line 66
    :cond_0
    new-array v0, v0, [B

    iput-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    .line 68
    :cond_1
    iget-wide v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->lz4fContext:J

    iget-object v3, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputBuffer:[B

    const/4 v4, 0x0

    iget v5, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    iget-object v6, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    const/4 v7, 0x0

    array-length v8, v6

    iget-object v9, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-static/range {v1 .. v9}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_compressUpdate(J[BII[BIILnet/jpountz/lz4/LZ4JNI$LZ4FError;)I

    move-result v0

    .line 71
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    const/4 v1, 0x0

    .line 74
    iput v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    .line 76
    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->out:Ljava/io/OutputStream;

    iget-object v3, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    invoke-virtual {v2, v3, v1, v0}, Ljava/io/OutputStream;->write([BII)V

    return-void
.end method


# virtual methods
.method public close()V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 124
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->compressBuffer()V

    .line 127
    iget-wide v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->lz4fContext:J

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    const/4 v3, 0x0

    array-length v4, v2

    iget-object v5, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-static/range {v0 .. v5}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_compressEnd(J[BIILnet/jpountz/lz4/LZ4JNI$LZ4FError;)I

    move-result v0

    .line 128
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    .line 129
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->out:Ljava/io/OutputStream;

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    invoke-virtual {v1, v2, v3, v0}, Ljava/io/OutputStream;->write([BII)V

    .line 131
    iget-wide v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->lz4fContext:J

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-static {v0, v1, v2}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_freeCompressionContext(JLnet/jpountz/lz4/LZ4JNI$LZ4FError;)V

    .line 132
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v0}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    .line 134
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->out:Ljava/io/OutputStream;

    invoke-virtual {v0}, Ljava/io/OutputStream;->close()V

    return-void
.end method

.method public flush()V
    .locals 6
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 110
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->compressBuffer()V

    .line 113
    iget-wide v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->lz4fContext:J

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    const/4 v3, 0x0

    array-length v4, v2

    iget-object v5, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-static/range {v0 .. v5}, Lnet/jpountz/lz4/LZ4JNI;->LZ4F_compressFlush(J[BIILnet/jpountz/lz4/LZ4JNI$LZ4FError;)I

    move-result v0

    .line 114
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->error:Lnet/jpountz/lz4/LZ4JNI$LZ4FError;

    invoke-virtual {v1}, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->check()V

    .line 115
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->out:Ljava/io/OutputStream;

    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->outputBuffer:[B

    invoke-virtual {v1, v2, v3, v0}, Ljava/io/OutputStream;->write([BII)V

    .line 117
    invoke-super {p0}, Ljava/io/FilterOutputStream;->flush()V

    return-void
.end method

.method public write(I)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 82
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputBuffer:[B

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    add-int/lit8 v2, v1, 0x1

    iput v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    int-to-byte p1, p1

    aput-byte p1, v0, v1

    .line 84
    array-length p1, v0

    if-ne v2, p1, :cond_0

    .line 85
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->compressBuffer()V

    :cond_0
    return-void
.end method

.method public write([BII)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    :goto_0
    if-lez p3, :cond_1

    .line 93
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputBuffer:[B

    array-length v0, v0

    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    sub-int/2addr v0, v1

    invoke-static {p3, v0}, Ljava/lang/Math;->min(II)I

    move-result v0

    .line 94
    iget-object v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputBuffer:[B

    iget v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    invoke-static {p1, p2, v1, v2, v0}, Ljava/lang/System;->arraycopy(Ljava/lang/Object;ILjava/lang/Object;II)V

    .line 95
    iget v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    add-int/2addr v1, v0

    iput v1, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputOffset:I

    .line 98
    iget-object v2, p0, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->inputBuffer:[B

    array-length v2, v2

    if-ne v1, v2, :cond_0

    .line 99
    invoke-direct {p0}, Lnet/jpountz/lz4/LZ4CompatibleOutputStream;->compressBuffer()V

    :cond_0
    add-int/2addr p2, v0

    sub-int/2addr p3, v0

    goto :goto_0

    :cond_1
    return-void
.end method
