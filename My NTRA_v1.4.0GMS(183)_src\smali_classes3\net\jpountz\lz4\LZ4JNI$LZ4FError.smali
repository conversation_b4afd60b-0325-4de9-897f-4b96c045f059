.class public Lnet/jpountz/lz4/LZ4JNI$LZ4FError;
.super Ljava/lang/Object;
.source "LZ4JNI.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lnet/jpountz/lz4/LZ4JNI;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "LZ4FError"
.end annotation


# instance fields
.field message:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 33
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 34
    iput-object v0, p0, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->message:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public check()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 43
    iget-object v0, p0, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->message:Ljava/lang/String;

    if-nez v0, :cond_0

    return-void

    .line 44
    :cond_0
    new-instance v0, Ljava/io/IOException;

    iget-object v1, p0, Lnet/jpountz/lz4/LZ4JNI$LZ4FError;->message:Ljava/lang/String;

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
.end method
