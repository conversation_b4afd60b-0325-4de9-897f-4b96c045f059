.class interface abstract Lnet/jpountz/xxhash/StreamingXXHash32$Factory;
.super Ljava/lang/Object;
.source "StreamingXXHash32.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lnet/jpountz/xxhash/StreamingXXHash32;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "Factory"
.end annotation


# virtual methods
.method public abstract newStreamingHash(I)Lnet/jpountz/xxhash/StreamingXXHash32;
.end method
