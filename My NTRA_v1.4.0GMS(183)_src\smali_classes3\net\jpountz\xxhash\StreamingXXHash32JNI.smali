.class final Lnet/jpountz/xxhash/StreamingXXHash32JNI;
.super Lnet/jpountz/xxhash/StreamingXXHash32;
.source "StreamingXXHash32JNI.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lnet/jpountz/xxhash/StreamingXXHash32JNI$Factory;
    }
.end annotation


# instance fields
.field private state:J


# direct methods
.method constructor <init>(I)V
    .locals 2

    .line 34
    invoke-direct {p0, p1}, Lnet/jpountz/xxhash/StreamingXXHash32;-><init>(I)V

    .line 35
    invoke-static {p1}, Lnet/jpountz/xxhash/XXHashJNI;->XXH32_init(I)J

    move-result-wide v0

    iput-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    return-void
.end method

.method private checkState()V
    .locals 4

    .line 39
    iget-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    const-wide/16 v2, 0x0

    cmp-long v0, v0, v2

    if-eqz v0, :cond_0

    return-void

    .line 40
    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    const-string v1, "Already finalized"

    invoke-direct {v0, v1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v0
.end method


# virtual methods
.method protected finalize()V
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/Throwable;
        }
    .end annotation

    .line 65
    invoke-super {p0}, Ljava/lang/Object;->finalize()V

    .line 67
    iget-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    invoke-static {v0, v1}, Lnet/jpountz/xxhash/XXHashJNI;->XXH32_free(J)V

    const-wide/16 v0, 0x0

    .line 68
    iput-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    return-void
.end method

.method public getValue()I
    .locals 2

    .line 53
    invoke-direct {p0}, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->checkState()V

    .line 54
    iget-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    invoke-static {v0, v1}, Lnet/jpountz/xxhash/XXHashJNI;->XXH32_digest(J)I

    move-result v0

    return v0
.end method

.method public reset()V
    .locals 2

    .line 46
    invoke-direct {p0}, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->checkState()V

    .line 47
    iget-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    invoke-static {v0, v1}, Lnet/jpountz/xxhash/XXHashJNI;->XXH32_free(J)V

    .line 48
    iget v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->seed:I

    invoke-static {v0}, Lnet/jpountz/xxhash/XXHashJNI;->XXH32_init(I)J

    move-result-wide v0

    iput-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    return-void
.end method

.method public update([BII)V
    .locals 2

    .line 59
    invoke-direct {p0}, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->checkState()V

    .line 60
    iget-wide v0, p0, Lnet/jpountz/xxhash/StreamingXXHash32JNI;->state:J

    invoke-static {v0, v1, p1, p2, p3}, Lnet/jpountz/xxhash/XXHashJNI;->XXH32_update(J[BII)V

    return-void
.end method
