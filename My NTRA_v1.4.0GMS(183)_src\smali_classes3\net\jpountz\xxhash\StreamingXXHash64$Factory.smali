.class interface abstract Lnet/jpountz/xxhash/StreamingXXHash64$Factory;
.super Ljava/lang/Object;
.source "StreamingXXHash64.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lnet/jpountz/xxhash/StreamingXXHash64;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "Factory"
.end annotation


# virtual methods
.method public abstract newStreamingHash(J)Lnet/jpountz/xxhash/StreamingXXHash64;
.end method
