.class public interface abstract Lnet/sqlcipher/Cursor;
.super Ljava/lang/Object;
.source "Cursor.java"

# interfaces
.implements Landroid/database/Cursor;


# static fields
.field public static final FIELD_TYPE_BLOB:I = 0x4

.field public static final FIELD_TYPE_FLOAT:I = 0x2

.field public static final FIELD_TYPE_INTEGER:I = 0x1

.field public static final FIELD_TYPE_NULL:I = 0x0

.field public static final FIELD_TYPE_STRING:I = 0x3


# virtual methods
.method public abstract getType(I)I
.end method
