.class public interface abstract Lnet/sqlcipher/database/SQLiteDatabase$LibraryLoader;
.super Ljava/lang/Object;
.source "SQLiteDatabase.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lnet/sqlcipher/database/SQLiteDatabase;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "LibraryLoader"
.end annotation


# virtual methods
.method public varargs abstract loadLibraries([Ljava/lang/String;)V
.end method
