.class public Lnet/sqlcipher/database/SQLiteException;
.super Lnet/sqlcipher/SQLException;
.source "SQLiteException.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 25
    invoke-direct {p0}, Lnet/sqlcipher/SQLException;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 28
    invoke-direct {p0, p1}, Lnet/sqlcipher/SQLException;-><init>(Ljava/lang/String;)V

    return-void
.end method
