.class public Lnet/sqlcipher/database/SQLiteMisuseException;
.super Lnet/sqlcipher/database/SQLiteException;
.source "SQLiteMisuseException.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 20
    invoke-direct {p0}, Lnet/sqlcipher/database/SQLiteException;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 23
    invoke-direct {p0, p1}, Lnet/sqlcipher/database/SQLiteException;-><init>(Ljava/lang/String;)V

    return-void
.end method
